# نظام كاش المحادثات - WhatsApp Cache System

## نظرة عامة

تم تطوير نظام كاش متقدم لحفظ المحادثات في WhatsApp، حيث يتم حفظ كل محادثة تحت مفتاح الـ WhatsApp ID الخاص بها.

## كيف يعمل النظام

### 1. مفتاح الكاش
```
whatsapp:messages:{whatsapp_id}
```
- كل رقم WhatsApp له مفتاح منفصل
- المحادثة تُحفظ كاملة تحت هذا المفتاح
- الرسائل الجديدة تُضاف للمحادثة الموجودة (append)

### 2. هيكل الرسالة في الكاش
```php
[
    'id' => 'message_id',
    'content' => 'نص الرسالة',
    'type' => 'text',
    'is_outgoing' => false, // true للرد التلقائي، false لرسالة المستخدم
    'role' => 'user', // user أو assistant للـ AI
    'sent_at' => '2025-07-08T10:30:00.000000Z',
    'cached_at' => '2025-07-08T10:30:05.000000Z'
]
```

### 3. إعدادات النظام
- **مدة الكاش**: 5 دقائق (تتجدد مع كل وصول)
- **حد الرسائل**: 20 رسالة كحد أقصى لكل محادثة
- **نظام Rolling**: الرسائل القديمة تُحذف تلقائياً

## الدوال المتاحة

### إضافة رسالة للمحادثة
```php
WhatsAppCacheService::addMessageToCache($whatsappId, [
    'id' => $messageId,
    'content' => $messageContent,
    'type' => 'text',
    'is_outgoing' => false,
    'sent_at' => now()->toISOString()
]);
```

### جلب المحادثة
```php
// للاستخدام العام
$messages = WhatsAppCacheService::getMessageHistory($whatsappId);

// للـ AI (تنسيق خاص)
$conversation = WhatsAppCacheService::getConversationForAI($whatsappId);
```

### إحصائيات المحادثة
```php
$stats = WhatsAppCacheService::getConversationStats($whatsappId);
```

### مسح المحادثة
```php
WhatsAppCacheService::clearMessageCache($whatsappId);
```

## أوامر الإدارة

### عرض إحصائيات عامة
```bash
php artisan whatsapp:cache-stats
```

### عرض تفاصيل محادثة محددة
```bash
php artisan whatsapp:cache-stats <EMAIL>
```

### مسح محادثة محددة
```bash
php artisan whatsapp:clear-cache <EMAIL>
```

### تشغيل queue worker
```bash
php artisan queue:work-whatsapp
```

## مثال على الاستخدام

### عند وصول رسالة جديدة
```php
// في WhatsAppController
WhatsAppCacheService::addMessageToCache($request->whatsapp_id, [
    'id' => $request->message_id,
    'content' => $request->message,
    'type' => 'text',
    'is_outgoing' => false,
    'sent_at' => now()->toISOString()
]);
```

### عند إرسال رد تلقائي
```php
// في WhatsappService
WhatsAppCacheService::addMessageToCache($whatsappId, [
    'id' => $whatsappId.'_'.time(),
    'content' => $aiResponse,
    'type' => 'text',
    'is_outgoing' => true,
    'sent_at' => now()->toISOString()
]);
```

### استخدام المحادثة في الـ AI
```php
// جلب المحادثة للـ AI
$conversation = WhatsAppCacheService::getConversationForAI($whatsappId);

// تمرير المحادثة للـ AI
$context = [
    'user_id' => $userId,
    'business_data' => $businessData,
    'message_history' => $conversation
];
```

## مميزات النظام

✅ **حفظ تلقائي**: كل رسالة تُحفظ تلقائياً  
✅ **ترتيب زمني**: الرسائل مرتبة حسب وقت الإرسال  
✅ **حد ذكي**: يحافظ على آخر 20 رسالة فقط  
✅ **تنسيق AI**: تنسيق خاص للذكاء الاصطناعي  
✅ **إحصائيات**: معلومات مفصلة عن كل محادثة  
✅ **أوامر إدارة**: أوامر سهلة للمراقبة والإدارة  

## ملاحظات مهمة

- النظام يعمل مع database cache driver
- الكاش يتجدد تلقائياً مع كل وصول
- الرسائل القديمة تُحذف تلقائياً للحفاظ على الأداء
- كل WhatsApp ID له محادثة منفصلة تماماً
