<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'telegram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('bot_token')->nullable();
            $table->string('bot_username')->nullable();
            $table->string('webhook_url')->nullable();
            $table->boolean('auto_reply')->default(false);
            $table->text('welcome_message')->nullable();
            $table->boolean('group_messages')->default(true);
            $table->boolean('private_messages')->default(true);
            $table->boolean('channel_posts')->default(false);
            $table->json('commands')->nullable(); // Bot commands
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('settings');
    }
};
