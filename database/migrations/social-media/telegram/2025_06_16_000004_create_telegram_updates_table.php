<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'telegram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('updates', function (Blueprint $table) {
            $table->id();
            $table->string('update_id')->unique(); // Telegram update ID
            $table->enum('type', ['message', 'edited_message', 'callback_query', 'inline_query']);
            $table->json('data'); // Full update data
            $table->boolean('processed')->default(false);
            $table->timestamp('received_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('updates');
    }
};
