<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'telegram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('telegram_id')->unique(); // Telegram user/chat ID
            $table->enum('type', ['user', 'group', 'supergroup', 'channel']);
            $table->string('username')->nullable();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('title')->nullable(); // For groups/channels
            $table->text('description')->nullable();
            $table->string('photo_url')->nullable();
            $table->boolean('is_bot')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->integer('members_count')->nullable(); // For groups/channels
            $table->json('metadata')->nullable(); // Additional info
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
