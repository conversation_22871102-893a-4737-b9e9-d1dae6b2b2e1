<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'telegram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id'); // Telegram message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'photo', 'video', 'audio', 'document', 'sticker', 'location', 'contact', 'voice']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For media messages
            $table->boolean('is_outgoing')->default(false); // true if sent by bot
            $table->boolean('is_edited')->default(false);
            $table->boolean('is_forwarded')->default(false);
            $table->json('reply_to')->nullable(); // Reply information
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('messages');
    }
};
