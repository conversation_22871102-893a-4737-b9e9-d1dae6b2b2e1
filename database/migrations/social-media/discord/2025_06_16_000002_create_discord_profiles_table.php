<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'discord';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('discord_id')->unique(); // Discord user/guild/channel ID
            $table->enum('type', ['user', 'guild', 'channel', 'role']);
            $table->string('name');
            $table->string('username')->nullable(); // For users
            $table->string('discriminator')->nullable(); // For users
            $table->string('avatar_url')->nullable();
            $table->text('description')->nullable();
            $table->boolean('is_bot')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->integer('members_count')->nullable(); // For guilds
            $table->string('parent_id')->nullable(); // For channels (guild ID)
            $table->json('metadata')->nullable(); // Additional info
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
