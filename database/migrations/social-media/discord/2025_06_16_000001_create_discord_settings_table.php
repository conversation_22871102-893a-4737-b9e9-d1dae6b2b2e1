<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'discord';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('bot_token')->nullable();
            $table->string('client_id')->nullable();
            $table->string('client_secret')->nullable();
            $table->boolean('auto_reply')->default(false);
            $table->text('welcome_message')->nullable();
            $table->boolean('slash_commands')->default(true);
            $table->boolean('message_commands')->default(true);
            $table->boolean('dm_messages')->default(true);
            $table->json('permissions')->nullable(); // Bot permissions
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('settings');
    }
};
