<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'discord';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Discord message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles (channel)
            $table->string('author_id'); // Discord user ID who sent the message
            $table->enum('type', ['text', 'embed', 'file', 'image', 'video', 'audio']);
            $table->text('content')->nullable(); // Message content
            $table->json('embeds')->nullable(); // Discord embeds
            $table->json('attachments')->nullable(); // File attachments
            $table->boolean('is_outgoing')->default(false); // true if sent by bot
            $table->boolean('is_edited')->default(false);
            $table->json('reactions')->nullable(); // Message reactions
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('messages');
    }
};
