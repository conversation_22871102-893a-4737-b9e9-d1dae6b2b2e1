<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'discord';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('commands', function (Blueprint $table) {
            $table->id();
            $table->string('command_id')->unique(); // Discord command ID
            $table->string('name');
            $table->text('description');
            $table->enum('type', ['slash', 'message', 'user']);
            $table->json('options')->nullable(); // Command options
            $table->boolean('is_global')->default(true);
            $table->string('guild_id')->nullable(); // For guild-specific commands
            $table->boolean('enabled')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('commands');
    }
};
