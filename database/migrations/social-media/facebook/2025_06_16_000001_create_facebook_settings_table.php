<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'facebook';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('page_id')->nullable();
            $table->string('access_token')->nullable();
            $table->string('app_id')->nullable();
            $table->string('app_secret')->nullable();
            $table->boolean('auto_post')->default(false);
            $table->boolean('auto_reply')->default(false);
            $table->boolean('auto_like')->default(false);
            $table->boolean('auto_comment')->default(false);
            $table->boolean('messenger_enabled')->default(true);
            $table->boolean('page_insights')->default(true);
            $table->integer('max_posts_per_day')->default(5);
            $table->text('welcome_message')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('settings');
    }
};
