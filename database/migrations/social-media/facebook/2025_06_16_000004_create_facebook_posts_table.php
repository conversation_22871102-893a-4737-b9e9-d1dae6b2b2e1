<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'facebook';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('post_id')->unique(); // Facebook post ID
            $table->enum('type', ['status', 'photo', 'video', 'link', 'event']);
            $table->text('message')->nullable(); // Post content
            $table->string('media_url')->nullable(); // For media posts
            $table->string('link_url')->nullable(); // For link posts
            $table->integer('likes_count')->default(0);
            $table->integer('comments_count')->default(0);
            $table->integer('shares_count')->default(0);
            $table->integer('reactions_count')->default(0);
            $table->json('reactions_breakdown')->nullable(); // Like, Love, Haha, etc.
            $table->boolean('is_published')->default(true);
            $table->timestamp('posted_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('posts');
    }
};
