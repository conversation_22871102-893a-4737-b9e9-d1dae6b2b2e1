<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'instagram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('instagram_id')->unique(); // Instagram user ID
            $table->string('username');
            $table->string('full_name')->nullable();
            $table->string('profile_picture_url')->nullable();
            $table->text('bio')->nullable();
            $table->integer('followers_count')->default(0);
            $table->integer('following_count')->default(0);
            $table->integer('posts_count')->default(0);
            $table->boolean('is_verified')->default(false);
            $table->boolean('is_business')->default(false);
            $table->boolean('is_following')->default(false);
            $table->boolean('follows_me')->default(false);
            $table->json('metadata')->nullable(); // Additional profile info
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
