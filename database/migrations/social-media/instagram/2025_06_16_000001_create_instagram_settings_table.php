<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'instagram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('username')->nullable();
            $table->string('access_token')->nullable();
            $table->string('user_id_instagram')->nullable(); // Instagram user ID
            $table->boolean('auto_post')->default(false);
            $table->boolean('auto_like')->default(false);
            $table->boolean('auto_comment')->default(false);
            $table->boolean('auto_follow')->default(false);
            $table->boolean('story_posting')->default(false);
            $table->integer('max_posts_per_day')->default(5);
            $table->json('hashtags')->nullable(); // Default hashtags
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('settings');
    }
};
