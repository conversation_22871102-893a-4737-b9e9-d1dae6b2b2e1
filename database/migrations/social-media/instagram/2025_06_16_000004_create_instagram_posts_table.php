<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'instagram';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('posts', function (Blueprint $table) {
            $table->id();
            $table->string('post_id')->unique(); // Instagram post ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['photo', 'video', 'carousel', 'story']);
            $table->text('caption')->nullable();
            $table->string('media_url');
            $table->integer('likes_count')->default(0);
            $table->integer('comments_count')->default(0);
            $table->json('hashtags')->nullable();
            $table->timestamp('posted_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('posts');
    }
};
