<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'whatsapp';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('whatsapp_settings_id')->constrained()->onDelete('cascade');
            $table->string('whatsapp_id')->unique(); // WhatsApp contact ID (e.g., <EMAIL>)
            $table->string('phone_number');
            $table->string('profile_name');
            $table->string('profile_picture_url')->nullable();
            $table->string('platform')->nullable();
            $table->boolean('is_client')->default(false); // to defrentiate between users and conncted users witch are the clients
            $table->boolean('is_online')->default(false);
            $table->timestamp('last_seen')->nullable();
            $table->timestamps();

            $table->index('whatsapp_settings_id');
            $table->index('whatsapp_id');
            $table->index('phone_number');
            $table->index('is_client');
            $table->index('is_online');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
