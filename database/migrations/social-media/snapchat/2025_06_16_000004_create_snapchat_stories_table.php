<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'snapchat';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('stories', function (Blueprint $table) {
            $table->id();
            $table->string('story_id')->unique(); // Snapchat story ID
            $table->enum('type', ['photo', 'video']);
            $table->string('media_url');
            $table->text('caption')->nullable();
            $table->json('filters')->nullable(); // Applied filters
            $table->integer('views_count')->default(0);
            $table->boolean('is_public')->default(true);
            $table->timestamp('expires_at'); // 24 hours from posting
            $table->timestamp('posted_at');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('stories');
    }
};
