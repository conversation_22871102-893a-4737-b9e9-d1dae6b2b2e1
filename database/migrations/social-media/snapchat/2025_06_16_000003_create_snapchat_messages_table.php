<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'snapchat';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('message_id')->unique(); // Snapchat message ID
            $table->unsignedBigInteger('profile_id'); // Foreign key to profiles
            $table->enum('type', ['text', 'snap', 'video_snap', 'audio_snap', 'sticker', 'bitmoji']);
            $table->text('content')->nullable(); // Message content
            $table->string('media_url')->nullable(); // For snap messages
            $table->boolean('is_outgoing')->default(false); // true if sent by us
            $table->boolean('is_opened')->default(false);
            $table->boolean('is_saved')->default(false);
            $table->integer('view_duration')->nullable(); // For snaps (seconds)
            $table->timestamp('expires_at')->nullable(); // When snap expires
            $table->timestamp('sent_at');
            $table->timestamps();

            $table->foreign('profile_id')->references('id')->on('profiles')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('messages');
    }
};
