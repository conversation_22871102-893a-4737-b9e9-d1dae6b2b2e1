<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'snapchat';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('settings', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id'); // Reference to main app user
            $table->boolean('enabled')->default(false);
            $table->boolean('connected')->default(false);
            $table->string('username')->nullable();
            $table->string('client_id')->nullable();
            $table->string('client_secret')->nullable();
            $table->string('access_token')->nullable();
            $table->string('refresh_token')->nullable();
            $table->boolean('auto_post')->default(false);
            $table->boolean('story_posting')->default(false);
            $table->boolean('spotlight_posting')->default(false);
            $table->integer('max_snaps_per_day')->default(10);
            $table->json('default_filters')->nullable(); // Default snap filters
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('settings');
    }
};
