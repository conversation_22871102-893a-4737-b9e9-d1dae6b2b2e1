<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    protected $connection = 'snapchat';

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::connection($this->connection)->create('profiles', function (Blueprint $table) {
            $table->id();
            $table->string('snapchat_id')->unique(); // Snapchat user ID
            $table->string('username');
            $table->string('display_name')->nullable();
            $table->string('bitmoji_url')->nullable();
            $table->boolean('is_friend')->default(false);
            $table->boolean('is_blocked')->default(false);
            $table->integer('snap_score')->nullable();
            $table->enum('friendship_status', ['pending', 'friends', 'blocked', 'none'])->default('none');
            $table->json('metadata')->nullable(); // Additional profile info
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::connection($this->connection)->dropIfExists('profiles');
    }
};
