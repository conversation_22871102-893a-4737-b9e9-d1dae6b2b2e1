<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clinics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('floor_number')->nullable();
            $table->string('room_number')->nullable(); // like 101 , 105 , 101-105
            $table->string('phone_number')->nullable();
            $table->timestamp('opening_time')->nullable();
            $table->timestamp('closing_time')->nullable();
            $table->boolean('is_open')->default(true);
            $table->string('closing_reason')->nullable();
            $table->timestamps();

            $table->index('business_id');
            $table->index('is_open');
            $table->index('opening_time');
            $table->index('closing_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clinics');
    }
};
