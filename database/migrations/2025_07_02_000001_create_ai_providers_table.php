<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up()
    {
        Schema::create('ai_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('base_url')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        Schema::create('api_keys', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ai_provider_id')->constrained('ai_providers')->onDelete('cascade');
            $table->string('api_key');
            $table->string('label')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        Schema::create('ai_capabilities', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::create('ai_models', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ai_provider_id')->constrained('ai_providers')->onDelete('cascade');
            $table->string('name');
            $table->string('model_avatar')->nullable();
            $table->string('model_salary')->default('free');
            $table->string('nickname')->nullable(); // like : Ahmed , Ali ,David
            $table->text('description')->nullable(); // i'm better in arabic chatting , analizing messages ,creating realistic images etc...
            $table->json('img_details')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
        });

        Schema::create('ai_model_capabilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('ai_model_id')->constrained('ai_models')->onDelete('cascade');
            $table->foreignId('ai_capability_id')->constrained('ai_capabilities')->onDelete('cascade');
            $table->text('system_message')->nullable();
            $table->unique(['ai_model_id', 'ai_capability_id']);
            $table->string('max_tokens')->default('500');
            $table->string('temperature')->default('0.6');
            $table->index(['ai_model_id', 'ai_capability_id']);
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('ai_providers');
        Schema::dropIfExists('api_keys');
        Schema::dropIfExists('ai_models');
    }
};
