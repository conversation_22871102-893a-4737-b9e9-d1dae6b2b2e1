<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->foreignId('business_id')->constrained()->onDelete('cascade');
            $table->foreignId('clinic_id')->constrained()->onDelete('cascade');
            $table->string('first_name');
            $table->string('last_name')->nullable();
            $table->string('specialization');
            $table->string('experience')->nullable();
            $table->string('education')->nullable();
            $table->json('certifications')->nullable();
            $table->json('languages')->nullable();
            $table->boolean('is_available')->default(true);
            $table->string('unavailability_reason')->nullable();
            $table->json('working_days')->nullable();
            $table->timestamp('available_from')->nullable();
            $table->timestamp('available_to')->nullable();
            $table->timestamps();

            $table->index('business_id');
            $table->index('clinic_id');
            $table->index('is_available');
            $table->index('available_from');
            $table->index('available_to');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
