<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */

    public function up(): void
    {
        Schema::create('customer_social_profiles', function (Blueprint $table) {
            $table->id();
            $table->morphs('sociable'); // sociable_id, sociable_type
            $table->string('platform'); // whatsapp, telegram, instagram, ...
            $table->unsignedBigInteger('profile_id'); // id for the profile in the external social database
            $table->timestamps();

            $table->index('sociable_id');
            $table->index('sociable_type');
            $table->index('platform');
            $table->index('profile_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customer_social_profiles');
    }
};
