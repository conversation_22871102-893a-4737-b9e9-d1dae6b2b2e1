<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('businesses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('description')->nullable();
            $table->string('website')->nullable();
            $table->string('email')->nullable();
            $table->string('phone');
            $table->string('address');
            $table->string('city');
            $table->string('state');
            $table->string('country');
            $table->string('logo')->nullable();
            $table->string('google_map_link')->nullable();
            $table->string('google_review_link')->nullable();
            $table->string('facebook_page_link')->nullable();
            $table->string('instagram_page_link')->nullable();
            $table->string('twitter_page_link')->nullable();
            $table->string('linkedin_page_link')->nullable();
            $table->string('youtube_channel_link')->nullable();
            $table->string('snapchat_page_link')->nullable();
            $table->string('tiktok_page_link')->nullable();
            $table->string('whatsapp_number')->nullable();
            $table->string('telegram_username')->nullable();
            $table->string('salla_store_url')->nullable();
            $table->timestamps();

            $table->index('user_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('businesses');
    }
};
