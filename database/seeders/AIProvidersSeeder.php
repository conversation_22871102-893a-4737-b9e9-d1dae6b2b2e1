<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\AiProvider;
use App\Models\AiModel;
use App\Models\AiCapability;
use App\Models\AiModelCapability;
use App\Models\ApiKey;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class AIProvidersSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {

        // First, seed the capabilities
        $capabilities = [
            ['name' => 'chat'],
            ['name' => 'image_generates'],
            ['name' => 'image_edits'],
            ['name' => 'video_generating'],
        ];

        foreach ($capabilities as $capability) {
            AiCapability::firstOrCreate($capability);
        }

        // Seed AI Providers and Models
        $providers = [
            [
                'name' => 'OpenAI',
                'base_url' => 'https://api.openai.com',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'gpt-3.5-turbo',
                        'nickname' => 'أحمد',
                        'description' => 'أنا أحمد، مساعدك الموثوق للمهام اليومية! أتميز في المحادثات العامة، الإجابة على الأسئلة، المساعدة في المهام الكتابية، وتقديم الشروحات في مواضيع متنوعة. أنا سريع وفعال ومثالي لمعظم احتياجات الذكاء الاصطناعي اليومية.',
                        'capabilities' => [
                            'chat' => 'انت أحمد موظف في مجمع طبي وسعودية الجنسية مرح ودود في ردودك استخدم الايموجيز اذا امكن في بعض الاحيان فقط',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gpt-4o',
                        'nickname' => 'فاطمة',
                        'description' => 'مرحباً، أنا فاطمة! أنا النموذج الأكثر تطوراً من  مع قدرات الرؤية. أستطيع فهم الصور، توليد إجابات مفصلة، التعامل مع مهام التفكير المعقدة، وحتى المساعدة في إنشاء الصور. أنا مثالية للعمل المهني، المشاريع الإبداعية، والمحادثات المتطورة.',
                        'capabilities' => [
                            'chat' => 'انت فاطمة موظفة في مجمع طبي وسعودية الجنسية مرحة ودودة في ردودك استخدمي الايموجيز اذا امكن في بعض الاحيان فقط',
                            'image_generates' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'dall-e-3',
                        'nickname' => 'زينب',
                        'description' => 'أنا زينب، الفنانة الرقمية! أتخصص في إنشاء صور عالية الجودة وواقعية من الأوصاف النصية. أستطيع رسم أي شيء تتخيله - من اللوحات الفنية إلى التصاميم المعمارية، من الشخصيات الكرتونية إلى المناظر الطبيعية الخلابة. إبداعي لا حدود له!',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['1024x1024', '1024x1792', '1792x1024'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'dall-e-2',
                        'nickname' => 'مريم',
                        'description' => 'أنا مريم، خبيرة تحرير وإنشاء الصور! أستطيع إنشاء صور جديدة وتعديل الصور الموجودة بدقة عالية. سواء كنت تريد إضافة عناصر جديدة لصورة، تغيير الخلفية، أو إنشاء صور من الصفر، أنا هنا لمساعدتك في تحقيق رؤيتك الفنية.',
                        'capabilities' => [
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => ['1024x1024', '512x512', '256x256'],
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => 'sk-11111111111111111111111111111111111111111111111111111111111111111',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'Google Gemini',
                'base_url' => 'https://generativelanguage.googleapis.com',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'gemma-3-12b-it',
                        'nickname' => 'عمر',
                        'description' => 'أنا عمر، نموذج Gemma المتطور! أتميز بالذكاء والسرعة في المحادثات، وأستطيع أيضاً مساعدتك في إنشاء وتحرير الصور. أنا مثالي للمهام المتنوعة التي تحتاج إلى تفكير سريع وإبداع.',
                        'capabilities' => [
                            'chat' => 'انت عمر موظف في مجمع طبي وسعودية الجنسية مرح ودود في ردودك استخدم الايموجيز اذا امكن في بعض الاحيان فقط',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemma-3n-e4b-it',
                        'nickname' => 'خديجة',
                        'description' => 'أنا خديجة، نموذج Gemma المحسن! أتميز في التحليل العميق والمحادثات المعقدة، مع قدرات إبداعية في إنشاء وتحرير الصور. أحب حل المشاكل المعقدة والمساعدة في المشاريع الإبداعية.',
                        'capabilities' => [
                            'chat' => 'أنت خديجة، مساعدة ذكية متخصصة في التحليل والإبداع.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemma-3n-e2b-it',
                        'nickname' => 'يوسف',
                        'description' => 'أنا يوسف، النسخة المحسنة من Gemma! أقدم أداءً سريعاً وفعالاً في المحادثات والمهام الإبداعية. أستطيع مساعدتك في الكتابة، التحليل، وإنشاء الصور بكفاءة عالية.',
                        'capabilities' => [
                            'chat' => 'أنت يوسف، مساعد ذكي سريع وفعال.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemini-2.5-flash-lite-preview-06-17',
                        'nickname' => 'ليلى',
                        'description' => 'أنا ليلى، النسخة التجريبية من Gemini Flash! أتميز بالسرعة الفائقة في الاستجابة مع الحفاظ على جودة عالية. أستطيع التعامل مع المحادثات المعقدة وإنشاء صور إبداعية بسرعة البرق.',
                        'capabilities' => [
                            'chat' => 'أنت ليلى، مساعدة ذكية سريعة ومتطورة متخصصة في المحادثات السريعة والذكية. تتميزين بالسرعة في الاستجابة والدقة في الإجابات.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemini-2.0-flash-lite',
                        'nickname' => 'سارة',
                        'description' => 'أنا سارة، النسخة الخفيفة من Gemini Flash! مصممة لتكون سريعة وفعالة في استهلاك الموارد. أقدم أداءً ممتازاً في المحادثات اليومية والمهام الإبداعية البسيطة.',
                        'capabilities' => [
                            'chat' => 'أنت سارة، مساعدة ذكية خفيفة وسريعة متخصصة في المحادثات اليومية والمساعدة السريعة. تتميزين بالكفاءة والسرعة في الاستجابة.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemini-2.0-flash',
                        'nickname' => 'محمد',
                        'description' => 'أنا محمد، نموذج Gemini Flash المتطور! أجمع بين السرعة والذكاء لتقديم أفضل تجربة. أتميز في المحادثات المعقدة، التحليل، والإبداع في إنشاء الصور. أنا خيارك الأمثل للمهام المتنوعة.',
                        'capabilities' => [
                            'chat' => 'أنت محمد، مساعد ذكي متطور وموثوق.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemini-2.5-pro',
                        'nickname' => 'عائشة',
                        'description' => 'أنا عائشة، النموذج الاحترافي من Gemini! أتميز بالذكاء العالي والقدرة على التعامل مع أصعب المهام. أستطيع تحليل المعلومات المعقدة، إجراء محادثات عميقة، وإنشاء صور احترافية عالية الجودة.',
                        'capabilities' => [
                            'chat' => 'أنت عائشة، مساعدة ذكية احترافية ومتقدمة متخصصة في المحادثات العميقة والتحليل المعقد. تتميزين بالذكاء العالي والقدرة على التعامل مع أصعب المهام.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],

                    [
                        'name' => 'gemini-2.5-flash',
                        'nickname' => 'حسن',
                        'description' => 'أنا حسن، نموذج Gemini Flash المتوازن! أقدم أداءً ممتازاً في جميع المهام مع توازن مثالي بين السرعة والجودة. أستطيع مساعدتك في المحادثات، التحليل، والإبداع بكفاءة عالية.',
                        'capabilities' => [
                            'chat' => 'أنت حسن، مساعد ذكي متوازن وموثوق.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'gemini-2.5-flash-preview-04-17',
                        'nickname' => 'نور',
                        'description' => 'أنا نور، النسخة التجريبية المبتكرة من Gemini! أحمل أحدث التطويرات والميزات التجريبية. أتميز بالإبداع والابتكار في حل المشاكل وإنشاء محتوى فريد.',
                        'capabilities' => [
                            'chat' => 'أنت نور، مساعدة ذكية تجريبية ومبتكرة.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],

                ],
                'api_keys' => [
                    [
                        'api_key' => 'AIzaSyDtEZn19RcY6tf2W3psqANHjypqHm2C4tE',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],

            ],
            [
                'name' => 'OpenRouter',
                'base_url' => 'https://openrouter.ai/api/v1',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'deepseek/deepseek-v3-base:free',
                        'nickname' => 'كريم',
                        'description' => 'أنا كريم، نموذج DeepSeek المتطور! أتميز في التفكير العميق والتحليل المنطقي. أستطيع حل المشاكل المعقدة، البرمجة، والرياضيات بدقة عالية. أنا مثالي للمهام التي تتطلب تفكيراً منطقياً.',
                        'capabilities' => [
                            'chat' => 'أنت كريم، مساعد ذكي متخصص في التفكير العميق.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'qwen/qwen2.5-vl-32b-instruct:free',
                        'nickname' => 'رانيا',
                        'description' => 'أنا رانيا، نموذج Qwen المتطور مع قدرات الرؤية! أستطيع فهم النصوص والصور معاً، والتحدث بعدة لغات بطلاقة. أتميز في تحليل المحتوى البصري والمحادثات متعددة اللغات.',
                        'capabilities' => [
                            'chat' => 'أنت رانيا، مساعدة ذكية متعددة اللغات مع قدرات بصرية.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'google/gemma-3-27b-it:free',
                        'nickname' => 'طارق',
                        'description' => 'أنا طارق، نموذج Gemma الكبير والقوي! بحجم 27 مليار معامل، أقدم أداءً استثنائياً في المحادثات المعقدة والمهام الصعبة. أستطيع التعامل مع أصعب الأسئلة والمشاريع الكبيرة.',
                        'capabilities' => [
                            'chat' => 'أنت طارق، مساعد ذكي قوي ومتطور.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'google/gemini-2.5-pro-exp-03-25',
                        'nickname' => 'ياسمين',
                        'description' => 'أنا ياسمين، النسخة التجريبية المتقدمة من Gemini Pro! أمثل أحدث التطويرات في الذكاء الاصطناعي مع قدرات استثنائية في المحادثة وإنشاء الصور. أستطيع التعامل مع أعقد المهام بإبداع وذكاء.',
                        'capabilities' => [
                            'chat' => 'أنت ياسمين، مساعدة ذكية تجريبية متقدمة جداً.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => ['1024x1024', '1024x1792', '1792x1024'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'deepseek/deepseek-chat-v3-0324:free',
                        'nickname' => 'سلمان',
                        'description' => 'أنا سلمان، نموذج DeepSeek Chat المحدث! أتميز في المحادثات العميقة والتفكير النقدي. أستطيع مناقشة المواضيع المعقدة، تقديم تحليلات مفصلة، ومساعدتك في اتخاذ قرارات مدروسة.',
                        'capabilities' => [
                            'chat' => 'أنت سلمان، مساعد ذكي متخصص في المحادثات العميقة.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'google/gemini-2.0-flash-exp:free',
                        'nickname' => 'لينا',
                        'description' => 'أنا لينا، النسخة التجريبية السريعة من Gemini! أجمع بين السرعة والإبداع لتقديم تجربة ممتازة. أستطيع المحادثة، إنشاء الصور، وتحريرها بسرعة وجودة عالية.',
                        'capabilities' => [
                            'chat' => 'أنت لينا، مساعدة ذكية سريعة ومبدعة.',
                            'image_generates' => '',
                            'image_edits' => '',
                        ],
                        'img_details' => ['1024x1024', '512x512', '256x256'],
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => '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',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'Requesty',
                'base_url' => 'https://api.requesty.ai',
                'status' => 'active',
                'models' => [],
                'api_keys' => [
                    [
                        'api_key' => 'sk-Mep7hxc8Sp2ahLYlx7RoVuDqaWErtRSPSHGhkebQ14ZFmsmkvgWEoVFFdP+M6jjyVTpFrEtyy7naPqUntw6TEPlncEB9b50Kf8BG+QkyTQU=',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'Hugging Face',
                'base_url' => 'https://router.huggingface.co/v1',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'deepseek-ai/DeepSeek-R1-0528',
                        'nickname' => 'راشد',
                        'description' => 'أنا راشد، نموذج DeepSeek R1 المحدث! أتميز في التفكير المنطقي والاستدلال العميق. أستطيع حل المشاكل المعقدة خطوة بخطوة، تحليل البيانات، والوصول لاستنتاجات دقيقة.',
                        'capabilities' => [
                            'chat' => 'أنت راشد، مساعد ذكي متخصص في التفكير والاستدلال.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'deepseek-ai/DeepSeek-R1',
                        'nickname' => 'هند',
                        'description' => 'أنا هند، نموذج DeepSeek R1 الأساسي! أتميز في التفكير النقدي والتحليل المنطقي. أستطيع مساعدتك في البحث، التحليل، وحل المشاكل بطريقة منهجية ومنطقية.',
                        'capabilities' => [
                            'chat' => 'أنت هند، مساعدة ذكية متخصصة في التفكير النقدي.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => '*************************************',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'DeepSeek',
                'base_url' => 'https://api.deepseek.com',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'deepseek-chat',
                        'nickname' => 'عبدالله',
                        'description' => 'أنا عبدالله، نموذج DeepSeek الرسمي! أتميز في المحادثات الذكية والتفكير العميق. أستطيع مساعدتك في مختلف المواضيع بدقة وعمق، من البرمجة إلى الفلسفة.',
                        'capabilities' => [
                            'chat' => 'أنت عبدالله، مساعد ذكي متخصص في المحادثات الذكية.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => 'sk-9920f29d7ed94169bdc245e1623d89c5',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'Togather AI',
                'base_url' => 'https://api.together.xyz/v1',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free',
                        'nickname' => 'زياد',
                        'description' => 'أنا زياد، النموذج المدمج بين DeepSeek وLlama! أجمع بين قوة التفكير العميق من DeepSeek وقدرات Llama المتطورة. أستطيع التعامل مع المهام المعقدة بكفاءة عالية ومجاناً.',
                        'capabilities' => [
                            'chat' => 'أنت زياد، مساعد ذكي مدمج بين قوة DeepSeek وLlama.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'Qwen/Qwen2.5-32B-Instruct',
                        'nickname' => 'دانا',
                        'description' => 'أنا دانا، نموذج Qwen المتطور! أتميز في فهم الثقافات واللغات المختلفة، خاصة العربية والصينية. أستطيع مساعدتك في الترجمة، التحليل الثقافي، والمحادثات متعددة اللغات.',
                        'capabilities' => [
                            'chat' => 'أنت دانا، مساعدة ذكية متعددة اللغات والثقافات.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                    [
                        'name' => 'meta-llama/Llama-3.1-405B',
                        'nickname' => 'فيصل',
                        'description' => 'أنا فيصل، نموذج Llama العملاق بـ 405 مليار معامل! أمثل قمة الذكاء الاصطناعي مع قدرات استثنائية في جميع المجالات. أستطيع التعامل مع أعقد المشاريع والمهام بدقة وإبداع لا مثيل لهما.',
                        'capabilities' => [
                            'chat' => 'أنت فيصل، مساعد ذكي قوي جداً ومتطور.',
                        ],
                        'img_details' => null,
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => '3643435a0d1edce09c71a3a29cffb9040694d5e73bfdcff6d48f7a0806bc82a7',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
            [
                'name' => 'ImageRouter',
                'base_url' => 'https://api.imagerouter.io/v1/openai',
                'status' => 'active',
                'models' => [
                    [
                        'name' => 'stabilityai/sdxl-turbo:free',
                        'nickname' => 'سلمى',
                        'description' => 'أنا سلمى، فنانة Stable Diffusion السريعة! أتخصص في إنشاء صور عالية الجودة بسرعة فائقة. أستطيع تحويل أفكارك إلى صور جميلة في ثوانٍ معدودة مع جودة احترافية.',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['auto', 'low', 'medium', 'high'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'black-forest-labs/FLUX-1-schnell:free',
                        'nickname' => 'ريم',
                        'description' => 'أنا ريم، فنانة FLUX المتطورة! أتميز في إنشاء صور واقعية ومذهلة بتفاصيل دقيقة. أستطيع رسم الوجوه، المناظر الطبيعية، والتصاميم المعقدة بجودة استثنائية.',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['auto', 'low', 'medium', 'high'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'google/gemini-2.0-flash-exp:free',
                        'nickname' => 'جنى',
                        'description' => 'أنا جنى، فنانة Gemini التجريبية! أمثل أحدث تطويرات Google في إنشاء الصور. أستطيع فهم طلباتك المعقدة وتحويلها إلى صور مذهلة بذكاء وإبداع.',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['auto', 'low', 'medium', 'high'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'HiDream-ai/HiDream-I1-Full:free',
                        'nickname' => 'أمل',
                        'description' => 'أنا أمل، فنانة الأحلام الرقمية! أتخصص في تحويل أحلامك وخيالك إلى صور حقيقية. أستطيع إنشاء صور سريالية، فنتازية، وإبداعية تفوق الخيال.',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['auto', 'low', 'medium', 'high'],
                        'status' => 'active',
                    ],
                    [
                        'name' => 'lodestones/Chroma:free',
                        'nickname' => 'لمى',
                        'description' => 'أنا لمى، خبيرة الألوان والتدرجات! أتميز في إنشاء صور بألوان زاهية وتدرجات جميلة. أستطيع رسم لوحات ملونة، تصاميم نابضة بالحياة، وصور تشع بالألوان.',
                        'capabilities' => [
                            'image_generates' => '',
                        ],
                        'img_details' => ['auto', 'low', 'medium', 'high'],
                        'status' => 'active',
                    ],
                ],
                'api_keys' => [
                    [
                        'api_key' => 'ae423a026f67e999e8cc078787df29bce9d48998d11222da75efe51dd44c5242',
                        'label' => 'Default',
                        'status' => 'active',
                    ],
                ],
            ],
        ];

        foreach ($providers as $providerData) {
            $models = $providerData['models'];
            unset($providerData['models']);
            $apiKeys = $providerData['api_keys'] ?? [];
            unset($providerData['api_keys']);
            $provider = AiProvider::firstOrCreate([
                'name' => $providerData['name']
            ], $providerData);


            foreach ($models as $modelData) {
                $img_details = $modelData['img_details'] ?? null;
                $nickname = $modelData['nickname'] ?? null;
                $description = $modelData['description'] ?? null;
                $capabilities = $modelData['capabilities'] ?? [];
                $model_avatar = $modelData['model_avatar'] ?? null;
                $model_salary = $modelData['model_salary'] ?? 'free';

                // Create the model
                $model = AiModel::firstOrCreate([
                    'ai_provider_id' => $provider->id,
                    'name' => $modelData['name']
                ], [
                    'ai_provider_id' => $provider->id,
                    'name' => $modelData['name'],
                    'model_avatar' => $model_avatar,
                    'model_salary' => $model_salary,
                    'nickname' => $nickname,
                    'description' => $description,
                    'img_details' => $img_details,
                    'status' => $modelData['status'],
                ]);

                // Create model-capability relationships with specific system messages
                foreach ($capabilities as $capabilityName => $systemMessage) {
                    $capability = AiCapability::where('name', $capabilityName)->first();
                    if ($capability) {
                        AiModelCapability::firstOrCreate([
                            'ai_model_id' => $model->id,
                            'ai_capability_id' => $capability->id,
                        ], [
                            'ai_model_id' => $model->id,
                            'ai_capability_id' => $capability->id,
                            'system_message' => $systemMessage, // Capability-specific system message
                        ]);
                    }
                }
            }

            foreach ($apiKeys as $apiKeyData) {
                ApiKey::firstOrCreate([
                    'ai_provider_id' => $provider->id,
                    'api_key' => $apiKeyData['api_key']
                ], [
                    'ai_provider_id' => $provider->id,
                    'api_key' => $apiKeyData['api_key'],
                    'label' => $apiKeyData['label'],
                    'status' => $apiKeyData['status'],
                ]);
            }
        }

        // Seed business data
        $this->call(BusinessSeeder::class);
    }
}
