/**
 * WhatsApp Messaging Handler
 * Handles sending messages through WhatsApp API
 * Uses message ACK events for status updates instead of recursive handling
 */

import { setupReverbConnection, cleanupReverbConnection, getUserId, getAuthToken, makeApiCall } from './whatsapp-helper.js';

let currentUserId = null;


/**
 * Initialize messaging system with Reverb connection
 */
export function initializeMessaging({ onAck, onMessageReceived }) {
    setupReverbConnection({
        listenerCalbacks: (data) => {
            // Handle different event types
            if (data.type === 'message_ack') {
                onAck(data.profile_id);
            } else if (data.type === 'message_received') {
                onMessageReceived(data.profile_id);
            }
        },
        eventName: 'whatsapp.messaging'
    });
}

/**
 * Cleanup messaging system
 */
export function cleanupMessaging() {
    cleanupReverbConnection();
    currentCallbacks = null;
}


/**
 * Internal function to send message
 * Message status updates will be handled by ACK events, not here
 */
async function sendMessage({ chatId, message, type, media, msg_id }) {

    currentUserId = getUserId();

    const messageData = {
        userId: currentUserId,
        chatId: chatId,
        message: message,
        type: type,
        media: media
    };

    try {
        console.log('📤 Sending WhatsApp message...');

        const res = await makeApiCall('/send-message', messageData, 'POST');

        console.log('✅ Message sent successfully. Status updates will come via ACK events.');

        return res;

    } catch (error) {
        console.error('❌ Failed to send message:', error);
    }
}


// Export utilities
export const WhatsAppMessaging = {
    initialize: initializeMessaging,
    cleanup: cleanupMessaging,
    sendMessage: sendMessage,

};

