/**
 * WhatsApp Authentication Handler
 * Handles all WhatsApp authentication states and API communication
 */

import { getUserId, setupReverbConnection, makeApiCall } from './whatsapp-helper.js';

let isConnecting = false;
let currentUserId = null;
let reverbChannel = null;
let authCallbacks = {};



/**
 * Connect to WhatsApp via API calls
 */
export function connectWhatsApp({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable, onUnauthenticated }) {
    // GUARD: Prevent multiple simultaneous connections
    if (isConnecting) {
        console.log('⚠️ WhatsApp connection already in progress, skipping...');
        return;
    }

    // Set connecting flag IMMEDIATELY to prevent race conditions
    isConnecting = true;

    currentUserId = getUserId();

    console.log('🚀 Starting WhatsApp connection for user:', currentUserId);

    // Safety timeout to reset flag if something goes wrong (30 seconds)
    setTimeout(() => {
        if (isConnecting) {
            console.log('⏰ Connection timeout - resetting isConnecting flag');
            isConnecting = false;
        }
    }, 30000);

    // Start with connecting status as default
    if (onConnecting) {
        onConnecting('Connecting to WhatsApp, please wait...');
    }

    // First, check if user session already exists
    checkExistingSession({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable, onUnauthenticated });
}

/**
 * Check if user session already exists on WhatsApp server or create new one
 */
function checkExistingSession({ onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable, onUnauthenticated }) {
    // Use the new check-connection endpoint that handles both checking and creating
    makeApiCall('/check-connection', { userId: currentUserId })
        .then(response => {
            console.log('📊 Connection check response:', response);

            const { status, message } = response;


            // If session exists and is already authenticated, use it
            if (status) {
                if (status.status === 'authenticated') {
                    isConnecting = false;
                    if (onAuthenticated && status.clientInfo) {
                        console.log('♻️ Using existing connected session with client info:', status.clientInfo);
                        onAuthenticated(status.clientInfo);
                    }
                }

                if (status.status === 'ready') {
                    isConnecting = false;
                    if (onReady && status.qrCode)
                        onReady(status.qrCode);
                }

                if (status.status === 'connecting') {
                    isConnecting = true;
                    if (onConnecting)
                        onConnecting(message ?? 'Connecting to WhatsApp, please wait...');
                }
                if (status.status === 'unauthenticated') {
                    isConnecting = false;
                    if (onUnauthenticated)
                        onUnauthenticated(message ?? 'WhatsApp session was lost due to browser navigation or reload. Please reconnect.');
                }

                // For any other status, start listening for updates
                startReverbListener({ onReady, onAuthenticating, onAuthenticated, onConnecting });
            } else {
                console.error('❌ WhatsApp server unavailable:', error.message);
                isConnecting = false;
                // Handle server unavailability
                handleServerUnavailable(error, { onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable });
            }

        }).catch(error => {
            console.error('❌ WhatsApp server unavailable:', error.message);
            isConnecting = false;

            // Handle server unavailability
            handleServerUnavailable(error, { onReady, onAuthenticating, onAuthenticated, onConnecting, onServerUnavailable });
        });
}

export function cancelSession() {
    console.log('❌ Requesting session cancellation...');
    isConnecting = false;
    makeApiCall('/cancel-session', { userId: currentUserId })
        .then(response => {
            if (response.preserved) {
                console.log('🔒 Session preserved - user is authenticated, keeping connection alive');
            } else {
                console.log('✅ Session cancelled:', response);
            }
        })
        .catch(error => {
            console.error('❌ WhatsApp server unavailable:', error.message);
        });
}

/**
 * Handle server unavailability
 */
function handleServerUnavailable(error, { onConnecting, onServerUnavailable }) {
    console.error('🚫 WhatsApp server is unavailable:', error.message);

    // Determine server status message based on error type
    let statusMessage = 'WhatsApp server is currently unavailable';

    if (error.message.includes('fetch')) {
        statusMessage = 'Cannot connect to WhatsApp server - server may be down or unreachable';
    } else if (error.message.includes('timeout')) {
        statusMessage = 'WhatsApp server connection timeout - server may be overloaded';
    } else if (error.message.includes('ECONNREFUSED')) {
        statusMessage = 'WhatsApp server connection refused - server is not running';
    } else if (error.message.includes('ENOTFOUND')) {
        statusMessage = 'WhatsApp server not found - check server configuration';
    } else if (error.message.includes('HTTP 500')) {
        statusMessage = 'WhatsApp server internal error - server is experiencing issues';
    } else if (error.message.includes('HTTP 503')) {
        statusMessage = 'WhatsApp server temporarily unavailable - server is under maintenance';
    }

    // Call the server unavailable callback if provided
    if (onServerUnavailable) {
        onServerUnavailable(statusMessage);
    } else if (onConnecting) {
        // Fallback to connecting callback if server unavailable callback not provided
        onConnecting('Unable to connect to WhatsApp server');
    }
}


/**
 * Disconnect WhatsApp session
 */
export function disconnectWhatsApp() {
    console.log('🔌 Disconnecting WhatsApp session for user:', currentUserId);

    return makeApiCall('/logout', {
        userId: currentUserId || getUserId()
    })
        .then(response => {
            console.log('✅ WhatsApp session disconnected:', response);

            // Clean up local state
            isConnecting = false;

            // Clean up Reverb channel
            if (reverbChannel) {
                try {
                    reverbChannel.unsubscribe();
                    console.log('🔗 Unsubscribed from Reverb channel');
                } catch (error) {
                    console.warn('⚠️ Error unsubscribing from Reverb channel:', error);
                }
                reverbChannel = null;
            }

            // Clear callbacks
            authCallbacks = {};

            return response;
        })
        .catch(error => {
            console.error('❌ Failed to disconnect WhatsApp:', error);
            throw error;
        });
}


/**
 * Start listening for auth status updates via Reverb
 */
function startReverbListener(callbacks) {
    authCallbacks = callbacks;
    setupReverbConnection({ listenerCalbacks: handleAuthStatus, eventName: 'whatsapp.auth' });
}


/**
 * Handle auth status updates
 */
function handleAuthStatus(statusData) {
    const { status, body } = statusData;

    switch (status) {
        case 'ready':
            if (authCallbacks.onReady && body) {
                authCallbacks.onReady(body); // body contains QR code
            }
            break;

        case 'authenticating':
            console.log('🔄 WhatsApp authenticating...');
            if (authCallbacks.onAuthenticating) {
                authCallbacks.onAuthenticating(body || 'Authenticating, please wait...'); // body contains progress message
            }
            break;

        case 'authenticated':
            console.log('✅ WhatsApp authentication in progress...');
            // Note: Device info usually comes in the subsequent 'ready' status
            if (body && typeof body === 'object' && body.pushname) {
                console.log('📱 Device info received:', body);
                isConnecting = false;
                if (authCallbacks.onAuthenticated) {
                    authCallbacks.onAuthenticated(body);
                }
            }
            break;

        case 'connecting':
            console.log('🔄 WhatsApp connecting...');
            if (authCallbacks.onConnecting) {
                authCallbacks.onConnecting(body || 'Connecting to WhatsApp, please wait...');
            }
            break;

        case 'unauthenticated':
            console.log('❌ WhatsApp unauthenticated:', body);
            if (authCallbacks.onUnauthenticated) {
                authCallbacks.onUnauthenticated(body);
            }
            break;
        default:
            console.log('🔄 WhatsApp connecting...');
            if (authCallbacks.onConnecting) {
                authCallbacks.onConnecting(body || 'Connecting to WhatsApp, please wait...');
            }
            break;
    }
}

/**
 * Get current user ID
 */
export function getCurrentUserId() {
    return currentUserId || getUserId();
}
