/**
 * WhatsApp Helper Functions
 * Shared utilities for WhatsApp functionality
 */

// Shared variables for Reverb connection
let reverbChannel = null;
let isListening = false;
let currentUserId = null;

// Get user ID from <PERSON><PERSON> (you can customize this based on your auth system)
function getUserId() {
    // Try to get user ID from meta tag
    const metaTag = document.querySelector('meta[name="user-id"]');
    if (metaTag) {
        return metaTag.getAttribute('content');
    }

    // Fallback: try to get from window.Laravel if available
    if (window.Laravel && window.Laravel.user && window.Laravel.user.id) {
        return window.Laravel.user.id.toString();
    }

    // Fallback: generate a session-based ID
    let sessionId = sessionStorage.getItem('whatsapp_session_id');
    if (!sessionId) {
        sessionId = 'session_' + Math.random().toString(36).substring(2, 11);
        sessionStorage.setItem('whatsapp_session_id', sessionId);
    }
    return sessionId;
}

// Get auth token (customize based on your auth system)
function getAuthToken() {
    const metaTag = document.querySelector('meta[name="csrf-token"]');
    return metaTag ? metaTag.getAttribute('content') : null;
}

function setupReverbConnection({ listenerCalbacks, eventName }) {

    if (isListening) {
        console.log('⚠️ Already listening, skipping setup');
        return;
    }

    if (!window.Echo) {
        console.error('❌ Echo not available, retrying in 1 second...');
        setTimeout(() => setupReverbConnection({ listenerCalbacks, eventName }), 1000);
        return;
    }

    currentUserId = getUserId();
    const channelName = `whatsapp.${currentUserId}`;

    try {
        console.log(`🔗 Setting up Reverb connection for channel: ${channelName}`);

        reverbChannel = window.Echo.private(channelName);

        // Add connection event listeners for debugging
        reverbChannel.subscribed(() => {
            console.log(`✅ Successfully subscribed to channel: ${channelName}`);
        });

        reverbChannel.error((error) => {
            console.error(`❌ Channel subscription error for ${channelName}:`, error);
        });
        
        const event = '.' + eventName;

        reverbChannel.listen(event, (data) => {
            console.log(`📨 Reverb event:${event} received:`, data);
            // Handle the event here too since it's working
            listenerCalbacks(data);
        });

        isListening = true;
        console.log(`✅ Listening for whatsapp events on channel: ${channelName}`);
        // const channels = window.Echo.connector.channels;
        // Object.keys(channels).forEach((channelName) => {
        //     console.log('Subscribed Channels ➡️', channelName);
        // });
    } catch (error) {
        console.error('❌ Failed to setup Reverb connection for WhatsApp:', error);
    }
}

function cleanupReverbConnection() {
    if (reverbChannel) {
        try {
            reverbChannel.stopListening('.whatsapp.messaging');
            reverbChannel.stopListening('.whatsapp.auth');
            window.Echo.leave(`whatsapp.${currentUserId}`);
            reverbChannel = null;
            isListening = false;
            console.log('🧹 Messaging Reverb connection cleaned up');
        } catch (error) {
            console.error('❌ Error cleaning up messaging Reverb connection:', error);
        }
    }
}


/**
 * Make API call to WhatsApp server
 */
async function makeApiCall(endpoint, data = null, method = 'POST') {
    const url = `http://localhost:3001${endpoint}`;

    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    };

    if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
    }

    try {
        const response = await fetch(url, options);

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            throw new Error(errorData.error || `HTTP ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        // Handle network errors (server down, connection refused, etc.)
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
            throw new Error('ECONNREFUSED - WhatsApp server is not running or unreachable');
        } else if (error.message.includes('timeout')) {
            throw new Error('timeout - WhatsApp server connection timeout');
        } else {
            // Re-throw the original error if it's not a network error
            throw error;
        }
    }
}
// Export functions
export { setupReverbConnection, cleanupReverbConnection, getUserId, getAuthToken, makeApiCall };
