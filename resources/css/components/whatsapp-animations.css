/* WhatsApp Settings Animations */
@keyframes scan-line {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(100%);
        opacity: 0;
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes glow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
    }
    50% {
        box-shadow: 0 0 30px rgba(34, 197, 94, 0.6);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.scan-animation::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, #10b981, transparent);
    animation: scan-line 2s infinite;
}

.float-animation {
    animation: float 3s ease-in-out infinite;
}

.glow-animation {
    animation: glow 2s ease-in-out infinite;
}

.gradient-border {
    position: relative;
    background: linear-gradient(45deg, #10b981, #06b6d4, #8b5cf6);
    padding: 2px;
    border-radius: 1.5rem;
}

.gradient-border::before {
    content: '';
    position: absolute;
    inset: 2px;
    background: white;
    border-radius: calc(1.5rem - 2px);
    z-index: -1;
}

.dark .gradient-border::before {
    background: #1e293b;
}
