<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use App\Services\SocialMediaServices\WhatsAppCacheService;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('queue:work-whatsapp', function () {
    $this->info('🚀 Starting WhatsApp Queue Worker...');
    $this->info('📋 Processing jobs from: whatsapp-ai, default queues');

    // Run the queue worker with specific configuration
    $this->call('queue:work', [
        '--queue' => 'whatsapp-ai,default',
        '--verbose' => true,
        '--tries' => 3,
        '--timeout' => 90,
        '--sleep' => 3,
        '--max-jobs' => 1000,
        '--max-time' => 3600, // 1 hour
    ]);
})->purpose('Start WhatsApp queue worker for processing AI responses and messages');

Artisan::command('whatsapp:cache-stats {whatsapp_id?}', function ($whatsappId = null) {
    if ($whatsappId) {
        // Show stats for specific WhatsApp ID
        $this->info("📊 Conversation Stats for WhatsApp ID: {$whatsappId}");
        $this->newLine();

        $stats = WhatsAppCacheService::getConversationStats($whatsappId);

        if (empty($stats) || $stats['total_messages'] == 0) {
            $this->warn('❌ No conversation found in cache for this WhatsApp ID');
            return;
        }

        $this->table(['Metric', 'Value'], [
            ['Total Messages', $stats['total_messages']],
            ['User Messages', $stats['user_messages']],
            ['Assistant Messages', $stats['assistant_messages']],
            ['Last Message At', $stats['last_message_at']],
            ['Conversation Started At', $stats['conversation_started_at']],
            ['Cache Key', $stats['cache_key']],
        ]);

        // Show recent messages
        $this->newLine();
        $this->info('💬 Recent Messages:');
        $messages = WhatsAppCacheService::getMessageHistory($whatsappId);
        $recentMessages = array_slice($messages, -5); // Last 5 messages

        foreach ($recentMessages as $msg) {
            $role = $msg['is_outgoing'] ? '🤖 Assistant' : '👤 User';
            $content = strlen($msg['content']) > 50 ? substr($msg['content'], 0, 50) . '...' : $msg['content'];
            $this->line("{$role}: {$content}");
        }

    } else {
        // Show general cache stats
        $this->info('📊 WhatsApp Cache Statistics');
        $this->newLine();

        $generalStats = WhatsAppCacheService::getCacheStats();
        $this->table(['Metric', 'Value'], [
            ['Business Cache Count', $generalStats['business_cache_count']],
            ['Message Cache Count', $generalStats['message_cache_count']],
            ['Total Cache Keys', $generalStats['total_cache_keys']],
        ]);

        $this->newLine();
        $this->comment('💡 Use: php artisan whatsapp:cache-stats {whatsapp_id} to see specific conversation');
    }
})->purpose('Show WhatsApp cache statistics and conversation details');

Artisan::command('whatsapp:clear-cache {whatsapp_id?}', function ($whatsappId = null) {
    if ($whatsappId) {
        // Clear specific conversation
        $this->warn("⚠️  This will clear the conversation cache for WhatsApp ID: {$whatsappId}");

        if ($this->confirm('Are you sure you want to continue?')) {
            WhatsAppCacheService::clearMessageCache($whatsappId);
            $this->info("✅ Conversation cache cleared for WhatsApp ID: {$whatsappId}");
        } else {
            $this->info('❌ Operation cancelled');
        }
    } else {
        $this->error('❌ Please provide a WhatsApp ID');
        $this->comment('Usage: php artisan whatsapp:clear-cache {whatsapp_id}');
    }
})->purpose('Clear conversation cache for specific WhatsApp ID');
