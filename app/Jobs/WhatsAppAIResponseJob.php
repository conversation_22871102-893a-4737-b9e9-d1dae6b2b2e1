<?php

namespace App\Jobs;

use App\Services\SocialMediaServices\WhatsappService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class WhatsAppAIResponseJob implements ShouldQueue
{

    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     */
    public $tries = 3;

    /**
     * The maximum number of seconds the job can run.
     */
    public $timeout = 120;

    protected $userId;
    protected $message;
    protected $whatsappId;
    protected $messageType;
    protected $businessData;

    /**
     * Create a new job instance.
     */
    public function __construct($userId, $message, $whatsappId, $messageType = 'text', $businessData = [])
    {
        $this->userId = $userId;
        $this->message = $message;
        $this->whatsappId = $whatsappId;
        $this->messageType = $messageType;
        $this->businessData = $businessData;

        // Set queue name for AI responses
        $this->onQueue('whatsapp-ai');
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            // Call the WhatsApp service to generate and send AI response
            WhatsappService::autoResponse(
                $this->userId,
                $this->message,
                $this->whatsappId,
                $this->messageType
            );
        } catch (\Exception $e) {
            Log::error('❌ WhatsApp AI response job failed', [
                'user_id' => $this->userId,
                'whatsapp_id' => $this->whatsappId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Re-throw to trigger job retry mechanism
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('💥 WhatsApp AI response job permanently failed after all retries', [
            'user_id' => $this->userId,
            'whatsapp_id' => $this->whatsappId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ]);
    }
}
