<?php

namespace App\Traits;

trait CreatesDatabaseFile
{
    /**
     * Create database file if it doesn't exist
     */
    public static function bootCreatesDatabaseFile()
    {
        static::creating(function ($model) {
            $model->ensureDatabaseExists();
        });
    }

    /**
     * Ensure the database file exists for this model's connection
     */
    public function ensureDatabaseExists()
    {
        $connection = $this->getConnectionName();
        
        if ($connection && $connection !== 'sqlite') {
            $config = config("database.connections.{$connection}");
            
            if ($config && $config['driver'] === 'sqlite') {
                $databasePath = $config['database'];
                
                if (!file_exists($databasePath)) {
                    // Create the database directory if it doesn't exist
                    $databaseDir = dirname($databasePath);
                    if (!is_dir($databaseDir)) {
                        mkdir($databaseDir, 0755, true);
                    }
                    
                    // Create empty SQLite file
                    touch($databasePath);
                }
            }
        }
    }

    /**
     * Static method to ensure database exists for a specific platform
     */
    public static function ensurePlatformDatabaseExists($platform)
    {
        $config = config("database.connections.{$platform}");
        
        if ($config && $config['driver'] === 'sqlite') {
            $databasePath = $config['database'];
            
            if (!file_exists($databasePath)) {
                // Create the database directory if it doesn't exist
                $databaseDir = dirname($databasePath);
                if (!is_dir($databaseDir)) {
                    mkdir($databaseDir, 0755, true);
                }
                
                // Create empty SQLite file
                touch($databasePath);
            }
        }
    }
}
