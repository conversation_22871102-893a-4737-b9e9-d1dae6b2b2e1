<?php

namespace App\Models\Instagram;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstagramMessage extends Model
{
    use HasFactory;

    protected $connection = 'instagram';
    protected $table = 'messages';

    protected $fillable = [
        'message_id',
        'profile_id',
        'type',
        'content',
        'media_url',
        'is_outgoing',
        'is_read',
        'sent_at',
    ];

    protected $casts = [
        'is_outgoing' => 'boolean',
        'is_read' => 'boolean',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the profile that owns this message
     */
    public function profile(): BelongsTo
    {
        return $this->belongsTo(InstagramProfile::class, 'profile_id');
    }
}
