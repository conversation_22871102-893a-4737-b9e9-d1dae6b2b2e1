<?php

namespace App\Models\Instagram;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class InstagramPost extends Model
{
    use HasFactory;

    protected $connection = 'instagram';
    protected $table = 'posts';

    protected $fillable = [
        'post_id',
        'profile_id',
        'type',
        'caption',
        'media_url',
        'likes_count',
        'comments_count',
        'hashtags',
        'posted_at',
    ];

    protected $casts = [
        'hashtags' => 'array',
        'posted_at' => 'datetime',
    ];

    /**
     * Get the profile that owns this post
     */
    public function profile(): BelongsTo
    {
        return $this->belongsTo(InstagramProfile::class, 'profile_id');
    }
}
