<?php

namespace App\Models\Instagram;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class InstagramSettings extends Model
{
    use HasFactory;

    protected $connection = 'instagram';
    protected $table = 'settings';

    protected $fillable = [
        'user_id',
        'enabled',
        'connected',
        'username',
        'access_token',
        'user_id_instagram',
        'auto_post',
        'auto_like',
        'auto_comment',
        'auto_follow',
        'story_posting',
        'max_posts_per_day',
        'hashtags',
    ];

    protected $casts = [
        'enabled' => 'boolean',
        'connected' => 'boolean',
        'auto_post' => 'boolean',
        'auto_like' => 'boolean',
        'auto_comment' => 'boolean',
        'auto_follow' => 'boolean',
        'story_posting' => 'boolean',
        'hashtags' => 'array',
    ];

    /**
     * Get the settings for a specific user
     */
    public static function forUser($userId)
    {
        return static::where('user_id', $userId)->first() ?? new static(['user_id' => $userId]);
    }
}
