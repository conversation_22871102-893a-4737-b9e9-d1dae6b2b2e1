<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasApiTokens;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'avatar_url',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    /**
     * Get the WhatsApp settings for this user
     */
    public function whatsappSettings(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(\App\Models\WhatsApp\WhatsAppSettings::class);
    }

    /**
     * Get all WhatsApp connected users for this user (through settings)
     */
    public function whatsappConnectedUsers(): \Illuminate\Database\Eloquent\Relations\HasManyThrough
    {
        return $this->hasManyThrough(
            \App\Models\WhatsApp\WhatsAppSettings::class,
            'user_id', // Foreign key on whatsapp_settings table
            'whatsapp_settings_id', // Foreign key on connected_users table
            'id', // Local key on users table
            'id' // Local key on whatsapp_settings table
        );
    }

    /**
     * Get all WhatsApp profiles for this user (through settings and connected users)
     */
    public function whatsappProfiles()
    {
        return \App\Models\WhatsApp\WhatsAppProfile::whereHas('connectedUser.waSettings', function($query) {
            $query->where('user_id', $this->id);
        });
    }

    /**
     * Get the businesses for this user
     */
    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class);
    }
}
