<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiModel extends Model
{
    use HasFactory;

    protected $fillable = [
        'ai_provider_id',
        'name',
        'model_avatar',
        'model_salary',
        'nickname',
        'description',
        'img_details',
        'status',
    ];

    protected $casts = [
        'img_details' => 'array',
    ];

    public function provider()
    {
        return $this->belongsTo(AiProvider::class, 'ai_provider_id');
    }

    public function capabilities()
    {
        return $this->belongsToMany(AiCapability::class, 'ai_model_capabilities')
            ->withPivot('system_message', 'max_tokens', 'temperature')
            ->withTimestamps();
    }

    public function modelCapabilities()
    {
        return $this->hasMany(AiModelCapability::class);
    }

    /**
     * Get system message for a specific capability
     */
    public function getSystemMessageForCapability(string $capabilityName): ?string
    {
        $capability = $this->capabilities()
            ->where('name', $capabilityName)
            ->first();

        return $capability ? $capability->pivot->system_message : null;
    }

    /**
     * Check if model has a specific capability
     */
    public function hasCapability(string $capabilityName): bool
    {
        return $this->capabilities()
            ->where('name', $capabilityName)
            ->exists();
    }

    /**
     * Get max tokens for a specific capability
     */
    public function getMaxTokensForCapability(string $capabilityName): ?int
    {
        $capability = $this->capabilities()
            ->where('name', $capabilityName)
            ->first();

        return $capability ? (int)$capability->pivot->max_tokens : null;
    }

    /**
     * Get temperature for a specific capability
     */
    public function getTemperatureForCapability(string $capabilityName): ?float
    {
        $capability = $this->capabilities()
            ->where('name', $capabilityName)
            ->first();

        return $capability ? (float)$capability->pivot->temperature : null;
    }

    /**
     * Get all capability names for this model
     */
    public function getCapabilityNames(): array
    {
        return $this->capabilities()->pluck('name')->toArray();
    }
}
