<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiCapability extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
    ];

    public function models()
    {
        return $this->belongsToMany(AiModel::class, 'ai_model_capabilities')
            ->withPivot('system_message')
            ->withTimestamps();
    }

    public function modelCapabilities()
    {
        return $this->hasMany(AiModelCapability::class);
    }
}
