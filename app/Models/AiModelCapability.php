<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AiModelCapability extends Model
{
    use HasFactory;

    protected $fillable = [
        'ai_model_id',
        'ai_capability_id',
        'system_message',
        'max_tokens',
        'temperature',
    ];

    public function model()
    {
        return $this->belongsTo(AiModel::class, 'ai_model_id');
    }

    public function capability()
    {
        return $this->belongsTo(AiCapability::class, 'ai_capability_id');
    }
}
