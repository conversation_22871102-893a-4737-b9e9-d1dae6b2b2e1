<?php

namespace App\Models\WhatsApp;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Log;

class WhatsAppProfile extends Model
{
    use HasFactory;

    protected $connection = 'whatsapp';
    protected $table = 'profiles';

    protected $fillable = [
        'whatsapp_settings_id',
        'whatsapp_id',
        'phone_number',
        'profile_name',
        'profile_picture_url',
        'platform',
        'is_online',
        'last_seen',
        'is_client',
    ];

    /**
     * Get the settings that owns this profile
     */
    public function waSettings(): BelongsTo
    {
        return $this->belongsTo(WhatsAppSettings::class, 'whatsapp_settings_id');
    }

    /**
     * Get all messages for this profile
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'profile_id');
    }

    /**
     * Get the latest message (replaces conversation table)
     */
    public function latestMessage()
    {
        return $this->messages()->latest('sent_at')->first();
    }

    /**
     * Get unread messages count
     */
    public function unreadCount(): int
    {
        return $this->messages()->where('is_outgoing', false)->where('is_read', false)->count();
    }

    /**
     * Update or create profile from client info
     * Handles WhatsApp ID conflicts by deleting old profiles
     */
    public static function updateOrCreateFromClientInfo($settingsId, $clientInfo)
    {
        if (isset($clientInfo['profile_picture_url']) && $clientInfo['profile_picture_url']) {
            $clientInfo['profile_picture_url'] = $clientInfo['profile_picture_url'] ?? null;
        }

        $whatsappId = $clientInfo['whatsapp_id'] ?? null;

        if ($whatsappId) {
            // Check if this WhatsApp ID is already used by another user
            $existingProfile = static::where('whatsapp_id', $whatsappId)
                ->where('whatsapp_settings_id', '!=', $settingsId)
                ->first();

            if ($existingProfile) {
                // Log::info('WhatsApp ID conflict detected in Laravel', [
                //     'whatsapp_id' => $whatsappId,
                //     'old_settings_id' => $existingProfile->whatsapp_settings_id,
                //     'new_settings_id' => $settingsId
                // ]);

                // Delete the entire WhatsApp settings (will cascade delete all related profiles)
                $oldSettingsId = $existingProfile->whatsapp_settings_id;
                $oldSettings = \App\Models\WhatsApp\WhatsAppSettings::find($oldSettingsId);
                if ($oldSettings) {
                    $oldSettings->delete();
                }

                // Log::info('Deleted old WhatsApp profile due to conflict', [
                //     'whatsapp_id' => $whatsappId,
                //     'deleted_settings_id' => $existingProfile->whatsapp_settings_id
                // ]);
            }
        }

        return static::updateOrCreate(
            ['whatsapp_settings_id' => $settingsId],
            $clientInfo
        );
    }
}
