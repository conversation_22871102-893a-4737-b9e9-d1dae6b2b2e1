<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Business extends Model
{
    protected $fillable = [
        'user_id',
        'name',
        'description',
        'website',
        'email',
        'phone',
        'address',
        'city',
        'state',
        'country',
        'logo',
        'google_map_link',
        'google_review_link',
        'facebook_page_link',
        'instagram_page_link',
        'twitter_page_link',
        'linkedin_page_link',
        'youtube_channel_link',
        'snapchat_page_link',
        'tiktok_page_link',
        'whatsapp_number',
        'telegram_username',
        'salla_store_url',
    ];

    /**
     * Get the user that owns the business.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the clinics for the business.
     */
    public function clinics(): HasMany
    {
        return $this->hasMany(Clinic::class);
    }

    /**
     * Get the doctors for the business.
     */
    public function doctors(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Doctor::class);
    }

    /**
     * Get the customers for the business.
     */
    public function customers(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Customer::class);
    }

    /**
     * Get the clinic services for the business.
     */
    public function clinicServices(): HasMany
    {
        return $this->hasMany(ClinicService::class);
    }
}
