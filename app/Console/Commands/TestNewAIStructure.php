<?php

namespace App\Console\Commands;

use App\Models\AiProvider;
use App\Models\AiModel;
use App\Models\AiCapability;
use App\Services\AIServices\AIServiceFactory;
use Illuminate\Console\Command;

class TestNewAIStructure extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'test:ai-structure {provider?}';

    /**
     * The console command description.
     */
    protected $description = 'Test the new AI structure with separated capabilities';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $providerName = $this->argument('provider') ?? 'google';
        
        $this->info("🧪 Testing New AI Structure");
        $this->line("Provider: {$providerName}");
        $this->line("");
        
        try {
            // Test capabilities
            $this->info("📋 Available Capabilities:");
            $capabilities = AiCapability::all();
            foreach ($capabilities as $capability) {
                $modelCount = $capability->models()->count();
                $this->line("  • {$capability->name} ({$modelCount} models)");
            }
            $this->line("");
            
            // Test provider and models
            $provider = AiProvider::where('name', 'like', "%{$providerName}%")->first();
            if (!$provider) {
                $this->error("❌ Provider not found: {$providerName}");
                return 1;
            }
            
            $this->info("🏢 Provider: {$provider->name}");
            $this->line("   Base URL: {$provider->base_url}");
            $this->line("   Status: {$provider->status}");
            $this->line("");
            
            // Test models with capabilities
            $models = $provider->models()->with('capabilities')->get();
            $this->info("🤖 Models ({$models->count()}):");
            
            foreach ($models as $model) {
                $this->line("  📱 {$model->name} ({$model->nickname})");
                $this->line("     Salary: {$model->mode_salary}");
                $this->line("     Status: {$model->status}");
                
                $capabilities = $model->capabilities;
                if ($capabilities->count() > 0) {
                    $this->line("     Capabilities:");
                    foreach ($capabilities as $capability) {
                        $systemMsg = $capability->pivot->system_message ? '✅' : '❌';
                        $this->line("       • {$capability->name} {$systemMsg}");
                        if ($capability->pivot->system_message) {
                            $preview = substr($capability->pivot->system_message, 0, 50) . '...';
                            $this->line("         \"{$preview}\"");
                        }
                    }
                } else {
                    $this->line("     ❌ No capabilities assigned");
                }
                $this->line("");
            }
            
            // Test AI service integration
            $this->info("🔧 Testing AI Service Integration:");
            $aiService = AIServiceFactory::create($provider->name);
            
            $chatModels = $aiService->getChatModels();
            $this->line("  Chat Models: {$chatModels->count()}");
            
            if ($chatModels->count() > 0) {
                $testModel = $chatModels->first();
                $this->line("  Testing model: {$testModel->name}");
                
                $systemMessage = $aiService->getModelSystemMessage($testModel->name, 'chat');
                $this->line("  System Message: " . ($systemMessage ? '✅ Found' : '❌ Not found'));
                
                if ($systemMessage) {
                    $preview = substr($systemMessage, 0, 80) . '...';
                    $this->line("  Content: \"{$preview}\"");
                }
            }
            
            $this->line("");
            $this->info("✅ New AI structure is working correctly!");
            
        } catch (\Exception $e) {
            $this->error("❌ Error: " . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
