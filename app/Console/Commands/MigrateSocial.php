<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class MigrateSocial extends Command
{
    protected $signature = 'migrate:social {platform} {mode?}';
    protected $description = 'Run migration for a specific social media database (optionally fresh).';

    public function handle()
    {
        $platform = $this->argument('platform');
        $mode = $this->argument('mode'); // could be 'fresh'

        $validPlatforms = [
            'whatsapp',
            'instagram',
            'telegram',
            'discord',
            'facebook',
            'snapchat'
        ];

        if (!in_array($platform, $validPlatforms)) {
            $this->error("Invalid platform: $platform");
            return;
        }


        $migrationPath = "database/migrations/social-media/{$platform}";


        if ($mode === 'fresh') {
            $this->info("Wiping and migrating database for: $platform");
            Artisan::call('db:wipe', ['--database' => $platform]);
            $this->line(Artisan::output());

            Artisan::call('migrate', [
                '--database' => $platform,
                '--path' => $migrationPath,
            ]);
            $this->line(Artisan::output());
        } else {
            $this->info("Running migration for: $platform");
            Artisan::call('migrate', [
                '--database' => $platform,
                '--path' => $migrationPath,
            ]);
            $this->line(Artisan::output());
        }

        $this->info("Done.");
    }
}
