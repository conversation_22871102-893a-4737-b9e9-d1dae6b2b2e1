<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class AiBotsTab extends Component
{
    public $openaiApiKey = '';
    public $claudeApiKey = '';
    public $geminiApiKey = '';
    
    public $defaultModel = 'gpt-3.5-turbo';
    public $maxTokens = 1000;
    public $temperature = 0.7;
    
    public $enableChatBot = true;
    public $enableAutoResponses = false;
    public $enableContentGeneration = true;
    public $enableImageGeneration = false;

    public $chatBotName = 'AI Assistant';
    public $chatBotPersonality = 'helpful';
    public $responseLanguage = 'en';

    public function mount()
    {
        // Load existing AI bot settings
        $user = auth()->user();
        if ($user) {
            // These would typically come from a settings table or user preferences
            $this->openaiApiKey = $user->openai_api_key ?? '';
            $this->claudeApiKey = $user->claude_api_key ?? '';
            $this->geminiApiKey = $user->gemini_api_key ?? '';
            $this->defaultModel = $user->default_ai_model ?? 'gpt-3.5-turbo';
            $this->maxTokens = $user->max_tokens ?? 1000;
            $this->temperature = $user->ai_temperature ?? 0.7;
            $this->chatBotName = $user->chatbot_name ?? 'AI Assistant';
            $this->chatBotPersonality = $user->chatbot_personality ?? 'helpful';
            $this->responseLanguage = $user->response_language ?? 'en';
        }
    }

    public function save()
    {
        $this->validate([
            'openaiApiKey' => 'nullable|string',
            'claudeApiKey' => 'nullable|string',
            'geminiApiKey' => 'nullable|string',
            'defaultModel' => 'required|string',
            'maxTokens' => 'required|integer|min:100|max:4000',
            'temperature' => 'required|numeric|min:0|max:2',
            'chatBotName' => 'required|string|max:100',
            'chatBotPersonality' => 'required|string',
            'responseLanguage' => 'required|string',
        ]);

        // Save AI bot settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'openai_api_key' => $this->openaiApiKey,
                'claude_api_key' => $this->claudeApiKey,
                'gemini_api_key' => $this->geminiApiKey,
                'default_ai_model' => $this->defaultModel,
                'max_tokens' => $this->maxTokens,
                'ai_temperature' => $this->temperature,
                'chatbot_name' => $this->chatBotName,
                'chatbot_personality' => $this->chatBotPersonality,
                'response_language' => $this->responseLanguage,
            ]);
        }

        session()->flash('message', 'AI Bot settings updated successfully!');
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function testConnection($provider)
    {
        // Test API connection
        session()->flash('message', 'Testing ' . ucfirst($provider) . ' connection...');
    }

    public function resetToDefaults()
    {
        $this->defaultModel = 'gpt-3.5-turbo';
        $this->maxTokens = 1000;
        $this->temperature = 0.7;
        $this->chatBotName = 'AI Assistant';
        $this->chatBotPersonality = 'helpful';
        $this->responseLanguage = 'en';
        
        session()->flash('message', 'Settings reset to defaults!');
    }

    public function render()
    {
        return view('livewire.settings.ai-bots-tab');
    }
}
