<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class PrivacyDataTab extends Component
{
    public $profileVisibility = 'public';
    public $emailVisibility = 'private';
    public $phoneVisibility = 'private';
    public $activityTracking = true;
    public $analyticsEnabled = true;
    public $marketingEmails = false;
    public $notificationEmails = true;
    public $dataRetentionPeriod = '2_years';
    public $twoFactorEnabled = false;
    public $sessionTimeout = 30;
    public $loginNotifications = true;

    public $visibilityOptions = [
        'public' => 'Public',
        'private' => 'Private',
        'friends' => 'Friends Only',
    ];

    public $retentionOptions = [
        '1_year' => '1 Year',
        '2_years' => '2 Years',
        '5_years' => '5 Years',
        'forever' => 'Forever',
    ];

    public function mount()
    {
        // Load existing privacy settings
        $user = auth()->user();
        if ($user) {
            $this->profileVisibility = $user->profile_visibility ?? 'public';
            $this->emailVisibility = $user->email_visibility ?? 'private';
            $this->phoneVisibility = $user->phone_visibility ?? 'private';
            $this->activityTracking = $user->activity_tracking ?? true;
            $this->analyticsEnabled = $user->analytics_enabled ?? true;
            $this->marketingEmails = $user->marketing_emails ?? false;
            $this->notificationEmails = $user->notification_emails ?? true;
            $this->dataRetentionPeriod = $user->data_retention_period ?? '2_years';
            $this->twoFactorEnabled = $user->two_factor_enabled ?? false;
            $this->sessionTimeout = $user->session_timeout ?? 30;
            $this->loginNotifications = $user->login_notifications ?? true;
        }
    }

    public function save()
    {
        $this->validate([
            'profileVisibility' => 'required|in:public,private,friends',
            'emailVisibility' => 'required|in:public,private,friends',
            'phoneVisibility' => 'required|in:public,private,friends',
            'dataRetentionPeriod' => 'required|in:1_year,2_years,5_years,forever',
            'sessionTimeout' => 'required|integer|min:5|max:1440',
        ]);

        // Save privacy settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'profile_visibility' => $this->profileVisibility,
                'email_visibility' => $this->emailVisibility,
                'phone_visibility' => $this->phoneVisibility,
                'activity_tracking' => $this->activityTracking,
                'analytics_enabled' => $this->analyticsEnabled,
                'marketing_emails' => $this->marketingEmails,
                'notification_emails' => $this->notificationEmails,
                'data_retention_period' => $this->dataRetentionPeriod,
                'two_factor_enabled' => $this->twoFactorEnabled,
                'session_timeout' => $this->sessionTimeout,
                'login_notifications' => $this->loginNotifications,
            ]);
        }

        session()->flash('message', 'Privacy & Data settings updated successfully!');
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function downloadData()
    {
        // Generate and download user data
        session()->flash('message', 'Data export will be sent to your email shortly!');
    }

    public function deleteAccount()
    {
        // This should show a confirmation modal first
        session()->flash('warning', 'Account deletion requires additional confirmation!');
    }

    public function clearActivityLog()
    {
        // Clear user activity log
        session()->flash('message', 'Activity log cleared successfully!');
    }

    public function revokeAllSessions()
    {
        // Revoke all active sessions except current
        session()->flash('message', 'All other sessions have been revoked!');
    }

    public function enableTwoFactor()
    {
        if ($this->twoFactorEnabled) {
            // Disable 2FA
            $this->twoFactorEnabled = false;
            session()->flash('message', 'Two-factor authentication disabled!');
        } else {
            // Enable 2FA - this would typically show a QR code setup
            $this->twoFactorEnabled = true;
            session()->flash('message', 'Two-factor authentication enabled!');
        }
    }

    public function render()
    {
        return view('livewire.settings.privacy-data-tab');
    }
}
