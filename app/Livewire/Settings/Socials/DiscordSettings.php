<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class DiscordSettings extends Component
{
    public $isConnected = false;
    public $botToken = '';
    public $clientId = '';
    public $clientSecret = '';
    public $guildId = '';
    public $enableSlashCommands = true;
    public $enableMessageCommands = true;
    public $enableAutoModeration = false;
    public $welcomeChannelId = '';
    public $logChannelId = '';
    public $welcomeMessage = 'Welcome to our Discord server, {user}!';
    public $autoRoleId = '';
    public $enableMusicBot = false;
    public $enableEconomyBot = false;
    public $enableLevelingSystem = false;
    public $maxMessagesPerMinute = 60;
    public $enableDMCommands = true;

    public function mount()
    {
        // Load existing Discord settings
        $user = auth()->user();
        if ($user) {
            $this->botToken = $user->discord_bot_token ?? '';
            $this->clientId = $user->discord_client_id ?? '';
            $this->clientSecret = $user->discord_client_secret ?? '';
            $this->guildId = $user->discord_guild_id ?? '';
            $this->enableSlashCommands = $user->discord_slash_commands ?? true;
            $this->enableMessageCommands = $user->discord_message_commands ?? true;
            $this->enableAutoModeration = $user->discord_auto_moderation ?? false;
            $this->welcomeChannelId = $user->discord_welcome_channel ?? '';
            $this->logChannelId = $user->discord_log_channel ?? '';
            $this->welcomeMessage = $user->discord_welcome_message ?? 'Welcome to our Discord server, {user}!';
            $this->autoRoleId = $user->discord_auto_role ?? '';
            $this->enableMusicBot = $user->discord_music_bot ?? false;
            $this->enableEconomyBot = $user->discord_economy_bot ?? false;
            $this->enableLevelingSystem = $user->discord_leveling_system ?? false;
            $this->maxMessagesPerMinute = $user->discord_max_messages ?? 60;
            $this->enableDMCommands = $user->discord_dm_commands ?? true;
            
            $this->isConnected = !empty($this->botToken) && !empty($this->clientId);
        }
    }

    public function save()
    {
        $this->validate([
            'botToken' => 'required|string',
            'clientId' => 'required|string|max:50',
            'clientSecret' => 'required|string',
            'guildId' => 'nullable|string|max:50',
            'welcomeChannelId' => 'nullable|string|max:50',
            'logChannelId' => 'nullable|string|max:50',
            'welcomeMessage' => 'required|string|max:1000',
            'autoRoleId' => 'nullable|string|max:50',
            'maxMessagesPerMinute' => 'required|integer|min:1|max:200',
        ]);

        // Save Discord settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'discord_bot_token' => $this->botToken,
                'discord_client_id' => $this->clientId,
                'discord_client_secret' => $this->clientSecret,
                'discord_guild_id' => $this->guildId,
                'discord_slash_commands' => $this->enableSlashCommands,
                'discord_message_commands' => $this->enableMessageCommands,
                'discord_auto_moderation' => $this->enableAutoModeration,
                'discord_welcome_channel' => $this->welcomeChannelId,
                'discord_log_channel' => $this->logChannelId,
                'discord_welcome_message' => $this->welcomeMessage,
                'discord_auto_role' => $this->autoRoleId,
                'discord_music_bot' => $this->enableMusicBot,
                'discord_economy_bot' => $this->enableEconomyBot,
                'discord_leveling_system' => $this->enableLevelingSystem,
                'discord_max_messages' => $this->maxMessagesPerMinute,
                'discord_dm_commands' => $this->enableDMCommands,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'Discord settings saved successfully!');
        $this->dispatch('close-modal');
    }

    public function testConnection()
    {
        // Test Discord Bot API connection
        session()->flash('message', 'Testing Discord bot connection...');
    }

    public function disconnect()
    {
        $this->botToken = '';
        $this->clientId = '';
        $this->clientSecret = '';
        $this->guildId = '';
        $this->isConnected = false;
        
        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'discord_bot_token' => null,
                'discord_client_id' => null,
                'discord_client_secret' => null,
                'discord_guild_id' => null,
            ]);
        }
        
        session()->flash('message', 'Discord disconnected successfully!');
        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.settings.socials.discord-settings');
    }
}
