<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class FacebookSettings extends Component
{
    public $isConnected = false;
    public $pageId = '';
    public $accessToken = '';
    public $appId = '';
    public $appSecret = '';
    public $autoPost = false;
    public $autoReply = false;
    public $autoLike = false;
    public $autoComment = false;
    public $postScheduling = true;
    public $enableMessenger = true;
    public $messengerAutoReply = false;
    public $messengerWelcomeMessage = 'Hello! Thanks for messaging us.';
    public $enablePageInsights = true;
    public $maxPostsPerDay = 5;
    public $commentTemplates = '';
    public $targetAudience = '';
    public $enableStoryPosting = false;
    public $enableEventCreation = false;

    public function mount()
    {
        // Load existing Facebook settings
        $user = auth()->user();
        if ($user) {
            $this->pageId = $user->facebook_page_id ?? '';
            $this->accessToken = $user->facebook_access_token ?? '';
            $this->appId = $user->facebook_app_id ?? '';
            $this->appSecret = $user->facebook_app_secret ?? '';
            $this->autoPost = $user->facebook_auto_post ?? false;
            $this->autoReply = $user->facebook_auto_reply ?? false;
            $this->autoLike = $user->facebook_auto_like ?? false;
            $this->autoComment = $user->facebook_auto_comment ?? false;
            $this->postScheduling = $user->facebook_post_scheduling ?? true;
            $this->enableMessenger = $user->facebook_messenger ?? true;
            $this->messengerAutoReply = $user->facebook_messenger_auto_reply ?? false;
            $this->messengerWelcomeMessage = $user->facebook_messenger_welcome ?? 'Hello! Thanks for messaging us.';
            $this->enablePageInsights = $user->facebook_page_insights ?? true;
            $this->maxPostsPerDay = $user->facebook_max_posts ?? 5;
            $this->commentTemplates = $user->facebook_comment_templates ?? '';
            $this->targetAudience = $user->facebook_target_audience ?? '';
            $this->enableStoryPosting = $user->facebook_story_posting ?? false;
            $this->enableEventCreation = $user->facebook_event_creation ?? false;
            
            $this->isConnected = !empty($this->pageId) && !empty($this->accessToken);
        }
    }

    public function save()
    {
        $this->validate([
            'pageId' => 'required|string|max:50',
            'accessToken' => 'required|string',
            'appId' => 'required|string|max:50',
            'appSecret' => 'required|string',
            'messengerWelcomeMessage' => 'nullable|string|max:500',
            'maxPostsPerDay' => 'required|integer|min:1|max:20',
            'commentTemplates' => 'nullable|string|max:2000',
            'targetAudience' => 'nullable|string|max:500',
        ]);

        // Save Facebook settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'facebook_page_id' => $this->pageId,
                'facebook_access_token' => $this->accessToken,
                'facebook_app_id' => $this->appId,
                'facebook_app_secret' => $this->appSecret,
                'facebook_auto_post' => $this->autoPost,
                'facebook_auto_reply' => $this->autoReply,
                'facebook_auto_like' => $this->autoLike,
                'facebook_auto_comment' => $this->autoComment,
                'facebook_post_scheduling' => $this->postScheduling,
                'facebook_messenger' => $this->enableMessenger,
                'facebook_messenger_auto_reply' => $this->messengerAutoReply,
                'facebook_messenger_welcome' => $this->messengerWelcomeMessage,
                'facebook_page_insights' => $this->enablePageInsights,
                'facebook_max_posts' => $this->maxPostsPerDay,
                'facebook_comment_templates' => $this->commentTemplates,
                'facebook_target_audience' => $this->targetAudience,
                'facebook_story_posting' => $this->enableStoryPosting,
                'facebook_event_creation' => $this->enableEventCreation,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'Facebook settings saved successfully!');
        $this->dispatch('close-modal');
    }

    public function testConnection()
    {
        // Test Facebook Graph API connection
        session()->flash('message', 'Testing Facebook connection...');
    }

    public function disconnect()
    {
        $this->pageId = '';
        $this->accessToken = '';
        $this->appId = '';
        $this->appSecret = '';
        $this->isConnected = false;
        
        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'facebook_page_id' => null,
                'facebook_access_token' => null,
                'facebook_app_id' => null,
                'facebook_app_secret' => null,
            ]);
        }
        
        session()->flash('message', 'Facebook disconnected successfully!');
        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.settings.socials.facebook-settings');
    }
}
