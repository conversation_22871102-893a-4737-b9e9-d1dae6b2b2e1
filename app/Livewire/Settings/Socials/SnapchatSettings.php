<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class SnapchatSettings extends Component
{
    public $isConnected = false;
    public $username = '';
    public $clientId = '';
    public $clientSecret = '';
    public $redirectUri = '';
    public $autoPost = false;
    public $enableStoryPosting = true;
    public $enableSpotlightPosting = false;
    public $enableLensSharing = false;
    public $maxSnapsPerDay = 10;
    public $enableBitmoji = true;
    public $enableSnapMap = false;
    public $enableMemories = true;
    public $autoSaveSnaps = false;
    public $enableChatBot = false;
    public $chatBotWelcomeMessage = 'Hey! Thanks for snapping us!';
    public $enableAnalytics = true;
    public $targetAudience = '';
    public $contentCategories = '';

    public function mount()
    {
        // Load existing Snapchat settings
        $user = auth()->user();
        if ($user) {
            $this->username = $user->snapchat_username ?? '';
            $this->clientId = $user->snapchat_client_id ?? '';
            $this->clientSecret = $user->snapchat_client_secret ?? '';
            $this->redirectUri = $user->snapchat_redirect_uri ?? '';
            $this->autoPost = $user->snapchat_auto_post ?? false;
            $this->enableStoryPosting = $user->snapchat_story_posting ?? true;
            $this->enableSpotlightPosting = $user->snapchat_spotlight_posting ?? false;
            $this->enableLensSharing = $user->snapchat_lens_sharing ?? false;
            $this->maxSnapsPerDay = $user->snapchat_max_snaps ?? 10;
            $this->enableBitmoji = $user->snapchat_bitmoji ?? true;
            $this->enableSnapMap = $user->snapchat_snap_map ?? false;
            $this->enableMemories = $user->snapchat_memories ?? true;
            $this->autoSaveSnaps = $user->snapchat_auto_save ?? false;
            $this->enableChatBot = $user->snapchat_chat_bot ?? false;
            $this->chatBotWelcomeMessage = $user->snapchat_chat_welcome ?? 'Hey! Thanks for snapping us!';
            $this->enableAnalytics = $user->snapchat_analytics ?? true;
            $this->targetAudience = $user->snapchat_target_audience ?? '';
            $this->contentCategories = $user->snapchat_content_categories ?? '';
            
            $this->isConnected = !empty($this->username) && !empty($this->clientId);
        }
    }

    public function save()
    {
        $this->validate([
            'username' => 'required|string|max:50',
            'clientId' => 'required|string|max:100',
            'clientSecret' => 'required|string',
            'redirectUri' => 'required|url',
            'maxSnapsPerDay' => 'required|integer|min:1|max:50',
            'chatBotWelcomeMessage' => 'nullable|string|max:500',
            'targetAudience' => 'nullable|string|max:500',
            'contentCategories' => 'nullable|string|max:500',
        ]);

        // Save Snapchat settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'snapchat_username' => $this->username,
                'snapchat_client_id' => $this->clientId,
                'snapchat_client_secret' => $this->clientSecret,
                'snapchat_redirect_uri' => $this->redirectUri,
                'snapchat_auto_post' => $this->autoPost,
                'snapchat_story_posting' => $this->enableStoryPosting,
                'snapchat_spotlight_posting' => $this->enableSpotlightPosting,
                'snapchat_lens_sharing' => $this->enableLensSharing,
                'snapchat_max_snaps' => $this->maxSnapsPerDay,
                'snapchat_bitmoji' => $this->enableBitmoji,
                'snapchat_snap_map' => $this->enableSnapMap,
                'snapchat_memories' => $this->enableMemories,
                'snapchat_auto_save' => $this->autoSaveSnaps,
                'snapchat_chat_bot' => $this->enableChatBot,
                'snapchat_chat_welcome' => $this->chatBotWelcomeMessage,
                'snapchat_analytics' => $this->enableAnalytics,
                'snapchat_target_audience' => $this->targetAudience,
                'snapchat_content_categories' => $this->contentCategories,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'Snapchat settings saved successfully!');
        $this->dispatch('close-modal');
    }

    public function testConnection()
    {
        // Test Snapchat API connection
        session()->flash('message', 'Testing Snapchat connection...');
    }

    public function disconnect()
    {
        $this->username = '';
        $this->clientId = '';
        $this->clientSecret = '';
        $this->redirectUri = '';
        $this->isConnected = false;
        
        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'snapchat_username' => null,
                'snapchat_client_id' => null,
                'snapchat_client_secret' => null,
                'snapchat_redirect_uri' => null,
            ]);
        }
        
        session()->flash('message', 'Snapchat disconnected successfully!');
        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.settings.socials.snapchat-settings');
    }
}
