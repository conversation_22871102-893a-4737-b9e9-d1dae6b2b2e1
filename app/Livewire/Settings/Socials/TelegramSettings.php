<?php

namespace App\Livewire\Settings\Socials;

use Livewire\Component;

class TelegramSettings extends Component
{
    public $isConnected = false;
    public $botToken = '';
    public $botUsername = '';
    public $webhookUrl = '';
    public $autoReply = false;
    public $welcomeMessage = 'Welcome to our Telegram bot!';
    public $helpMessage = 'Here are the available commands...';
    public $enableInlineMode = true;
    public $enableGroupMessages = true;
    public $enableChannelPosts = false;
    public $maxMessagesPerMinute = 30;
    public $enableFileUploads = true;
    public $maxFileSize = 50; // MB
    public $allowedFileTypes = 'jpg,jpeg,png,gif,pdf,doc,docx';
    public $enableNotifications = true;
    public $adminChatId = '';

    public function mount()
    {
        // Load existing Telegram settings
        $user = auth()->user();
        if ($user) {
            $this->botToken = $user->telegram_bot_token ?? '';
            $this->botUsername = $user->telegram_bot_username ?? '';
            $this->webhookUrl = $user->telegram_webhook_url ?? '';
            $this->autoReply = $user->telegram_auto_reply ?? false;
            $this->welcomeMessage = $user->telegram_welcome_message ?? 'Welcome to our Telegram bot!';
            $this->helpMessage = $user->telegram_help_message ?? 'Here are the available commands...';
            $this->enableInlineMode = $user->telegram_inline_mode ?? true;
            $this->enableGroupMessages = $user->telegram_group_messages ?? true;
            $this->enableChannelPosts = $user->telegram_channel_posts ?? false;
            $this->maxMessagesPerMinute = $user->telegram_max_messages ?? 30;
            $this->enableFileUploads = $user->telegram_file_uploads ?? true;
            $this->maxFileSize = $user->telegram_max_file_size ?? 50;
            $this->allowedFileTypes = $user->telegram_allowed_files ?? 'jpg,jpeg,png,gif,pdf,doc,docx';
            $this->enableNotifications = $user->telegram_notifications ?? true;
            $this->adminChatId = $user->telegram_admin_chat_id ?? '';
            
            $this->isConnected = !empty($this->botToken) && !empty($this->botUsername);
        }
    }

    public function save()
    {
        $this->validate([
            'botToken' => 'required|string',
            'botUsername' => 'required|string|max:50',
            'webhookUrl' => 'nullable|url',
            'welcomeMessage' => 'required|string|max:1000',
            'helpMessage' => 'required|string|max:2000',
            'maxMessagesPerMinute' => 'required|integer|min:1|max:100',
            'maxFileSize' => 'required|integer|min:1|max:100',
            'allowedFileTypes' => 'required|string|max:200',
            'adminChatId' => 'nullable|string|max:50',
        ]);

        // Save Telegram settings
        $user = auth()->user();
        if ($user) {
            $user->update([
                'telegram_bot_token' => $this->botToken,
                'telegram_bot_username' => $this->botUsername,
                'telegram_webhook_url' => $this->webhookUrl,
                'telegram_auto_reply' => $this->autoReply,
                'telegram_welcome_message' => $this->welcomeMessage,
                'telegram_help_message' => $this->helpMessage,
                'telegram_inline_mode' => $this->enableInlineMode,
                'telegram_group_messages' => $this->enableGroupMessages,
                'telegram_channel_posts' => $this->enableChannelPosts,
                'telegram_max_messages' => $this->maxMessagesPerMinute,
                'telegram_file_uploads' => $this->enableFileUploads,
                'telegram_max_file_size' => $this->maxFileSize,
                'telegram_allowed_files' => $this->allowedFileTypes,
                'telegram_notifications' => $this->enableNotifications,
                'telegram_admin_chat_id' => $this->adminChatId,
            ]);
        }

        $this->isConnected = true;
        session()->flash('message', 'Telegram settings saved successfully!');
        $this->dispatch('close-modal');
    }

    public function testConnection()
    {
        // Test Telegram Bot API connection
        session()->flash('message', 'Testing Telegram bot connection...');
    }

    public function disconnect()
    {
        $this->botToken = '';
        $this->botUsername = '';
        $this->webhookUrl = '';
        $this->isConnected = false;
        
        // Update database
        $user = auth()->user();
        if ($user) {
            $user->update([
                'telegram_bot_token' => null,
                'telegram_bot_username' => null,
                'telegram_webhook_url' => null,
            ]);
        }
        
        session()->flash('message', 'Telegram disconnected successfully!');
        $this->dispatch('close-modal');
    }

    public function render()
    {
        return view('livewire.settings.socials.telegram-settings');
    }
}
