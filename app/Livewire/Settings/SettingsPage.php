<?php

namespace App\Livewire\Settings;

use Livewire\Component;

class SettingsPage extends Component
{
    public $activeTab = 'account';

    public function setActiveTab($tab)
    {
        $this->activeTab = $tab;
    }

    public function render()
    {
        return view('livewire.settings.settings-page')
            ->layout(
                'components.app-layout',
                [
                    'title' => 'Settings',
                    'isHeaderBlur' => 'true'
                ]
            );
    }
}
