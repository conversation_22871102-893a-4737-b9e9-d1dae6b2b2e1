<?php

namespace App\Livewire\Settings;

use Livewire\Component;
use Livewire\WithFileUploads;

class AccountTab extends Component
{
    use WithFileUploads;

    public $displayName = '';
    public $fullName = '';
    public $email = '';
    public $phoneNumber = '';
    public $avatar_url;

    public function mount()
    {
        // Initialize with current user data
        $user = auth()->user();
        if ($user) {
            $this->displayName = $user->name ?? '';
            $this->fullName = $user->name ?? '';
            $this->email = $user->email ?? '';
            $this->phoneNumber = $user->phone ?? '';
            $this->avatar_url = $user->avatar_url ?? '';
        }
    }

    public function save()
    {
        $this->validate([
            'displayName' => 'required|string|max:255',
            'fullName' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phoneNumber' => 'nullable|string|max:20',
            'avatar_url' => 'nullable|image|max:1024', // 1MB Max
        ]);

        // Update user data
        $user = auth()->user();
        if ($user) {
            $user->update([
                'name' => $this->fullName,
                'email' => $this->email,
                'phone' => $this->phoneNumber,
            ]);

            // Handle avatar_url upload if provided
            if ($this->avatar_url) {
                $avatar_urlPath = $this->avatar_url->store('avatar_urls', 'public');
                $user->update(['avatar_url' => $avatar_urlPath]);
            }
        }

        session()->flash('message', 'Account settings updated successfully!');
    }

    public function cancel()
    {
        $this->mount(); // Reset to original values
    }

    public function connectGoogle()
    {
        // Handle Google OAuth connection
        session()->flash('message', 'Google connection feature coming soon!');
    }

    public function render()
    {
        return view('livewire.settings.account-tab');
    }
}
