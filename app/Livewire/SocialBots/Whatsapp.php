<?php

namespace App\Livewire\SocialBots;

use Livewire\Component;
use App\Models\WhatsApp\{WhatsAppProfile, WhatsAppMessage, WhatsAppSettings};
use Illuminate\Support\Facades\{DB, Auth, Log};
use Carbon\Carbon;
use Livewire\Attributes\On;

class Whatsapp extends Component
{
    public $conversations = [];
    public $selectedConversation = null;
    public $messages = [];
    public $newMessage = '';
    public $connectedClientInfo = null;
    public $whatsappSettings = null;

    public function mount()
    {
        // Ensure conversations is always an array
        $this->conversations = [];
        $this->messages = [];
        $this->loadConnectedClientInfo();
        $this->dispatch('load-conversations');
    }


    public function loadConnectedClientInfo()
    {
        $whatsappSettings = WhatsappSettings::forUserSelect(Auth::id(), ['id']);

        if (!$whatsappSettings) {
            $this->connectedClientInfo = null;
            return;
        }

        try {
            $this->whatsappSettings = $whatsappSettings;
            $clientProfile = WhatsAppProfile::where('whatsapp_settings_id', $whatsappSettings->id)
                ->where('is_client', true)
                ->first();

            if ($clientProfile) {
                $this->connectedClientInfo = [
                    'whatsapp_id' => $clientProfile->whatsapp_id,
                    'phone_number' => $clientProfile->phone_number,
                    'profile_name' => $clientProfile->profile_name,
                    'profile_picture_url' => $clientProfile->profile_picture_url,
                    'is_client' => $clientProfile->is_client,
                    'platform' => $clientProfile->platform,
                ];
            } else {
                $this->connectedClientInfo = null;
            }
        } catch (\Exception $e) {
            Log::error('Error loading connected client info: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->connectedClientInfo = null;
        }
    }

    #[On('load-conversations')]
    public function loadConversations()
    {
        try {
            $conversations = WhatsAppProfile::select([
                'profiles.*',
                'last_messages.content as last_message_content',
                'last_messages.type as last_message_type',
                'last_messages.sent_at as last_message_time',
                DB::raw('COUNT(CASE WHEN messages.status != "read" AND messages.is_outgoing = 0 THEN 1 END) as unread_count')
            ])
                ->where('profiles.whatsapp_settings_id', $this->whatsappSettings->id)
                ->where('is_client', false)
                ->leftJoin('messages', 'profiles.id', '=', 'messages.profile_id')
                ->leftJoin(
                    'messages as last_messages',
                    fn($join) =>
                    $join->on('profiles.id', '=', 'last_messages.profile_id')
                        ->whereRaw('last_messages.id = (SELECT MAX(id) FROM messages WHERE messages.profile_id = profiles.id)')
                )
                ->groupBy([
                    'profiles.id',
                    'profiles.whatsapp_id',
                    'profiles.profile_name',
                    'profiles.phone_number',
                    'profiles.profile_picture_url',
                    'profiles.is_online',
                    'profiles.last_seen',
                    'last_messages.content',
                    'last_messages.type',
                    'last_messages.sent_at',
                ])
                ->orderBy('last_messages.sent_at', 'desc')
                ->orderBy('profiles.id', 'desc') // Secondary sort by profile ID for conversations without messages
                ->get()
                ->map(fn($c) => $this->formatConversation($c))
                ->filter() // Remove any null values
                ->toArray();

            // Ensure we always have an array and sort by latest message time
            if (is_array($conversations)) {
                usort($conversations, function ($a, $b) {
                    $timestampA = $a['last_message_timestamp'] ?? 0;
                    $timestampB = $b['last_message_timestamp'] ?? 0;

                    // Sort by timestamp (newest first)
                    if ($timestampA !== $timestampB) {
                        return $timestampB - $timestampA;
                    }

                    // If timestamps are equal, sort by ID (newer profiles first)
                    return ($b['id'] ?? 0) - ($a['id'] ?? 0);
                });

                $this->conversations = $conversations;
            } else {
                $this->conversations = [];
            }
        } catch (\Exception $e) {
            Log::error('Error loading conversations: ' . $e->getMessage(), [
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);
            $this->conversations = [];
        }
    }

    public function loadConversationMessages($profileId)
    {

        if (!$this->whatsappSettings) return [];
        $this->selectedConversation = collect($this->conversations)->firstWhere('id', $profileId);

        // dd($this->selectedConversation);

        $this->messages = WhatsAppMessage::where('profile_id', $profileId)
            ->with('attachments')
            ->orderBy('sent_at', 'asc')
            ->get()
            ->map(fn($m) => $this->formatMessage($m))
            ->toArray();

        // Mark as read and refresh conversations
        WhatsAppMessage::where('profile_id', $profileId)
            ->where('is_outgoing', false)
            ->where('status', '!=', 'read')
            ->update(['status' => 'read']);

        $this->updateSingleConversation($profileId);

        return $this->messages;
    }

    public function refreshMessages($profileId)
    {
        if (!$this->validateProfileAccess($profileId)) return;
        // Force reload single conversation to ensure proper sorting
        $this->updateSingleConversation($profileId);
        // dd('selected Conversation : ', $this->selectedConversation, 'profile recived : ', $profileId);
        // if conversation is open then load conversation messages and there is a sorting inside it
        if ($this->selectedConversation && isset($this->selectedConversation['id']) && $this->selectedConversation['id'] == $profileId) {
            $this->loadConversationMessages($profileId);
        }
    }

    public function getConversationAttachmentsProperty()
    {
        // Ensure messages is an array
        if (!is_array($this->messages)) {
            return collect();
        }

        return collect($this->messages)
            ->pluck('attachments')
            ->flatten(1)
            ->filter();
    }

    private function validateProfileAccess($profileId)
    {
        return $this->whatsappSettings && WhatsAppProfile::where('id', $profileId)
            ->where('whatsapp_settings_id', $this->whatsappSettings->id)
            ->exists();
    }

    private function formatConversation($conversation)
    {
        // Ensure conversation object exists
        if (!$conversation) {
            return null;
        }

        $lastMessageTime = $conversation->last_message_time;
        $formattedTime = '';

        if ($lastMessageTime) {
            $formattedTime = Carbon::parse($lastMessageTime)->format('g:i A');
        }

        $last_seen = $conversation->last_seen;
        $last_seen_time = null;

        if ($last_seen) {
            $last_seen_time = 'last seen ' . Carbon::parse($conversation->last_seen)->diffForHumans();
        }

        return [
            'id' => $conversation->id ?? null,
            'whatsapp_id' => $conversation->whatsapp_id ?? null,
            'profile_name' => $conversation->profile_name ?: ($conversation->phone_number ?? 'Unknown'),
            'phone_number' => $conversation->phone_number ?? '',
            'profile_picture_url' => $conversation->profile_picture_url ?: asset('images/200x200.png'),
            'is_online' => $conversation->is_online ?? false,
            'last_seen' => $last_seen_time,
            'last_message_content' => $conversation->last_message_content ?? '',
            'last_message_type' => $conversation->last_message_type ?? '',
            'last_message_time' => $formattedTime,
            'last_message_timestamp' => strtotime($lastMessageTime), // Raw timestamp for sorting
            'unread_count' => $conversation->unread_count ?? 0
        ];
    }

    private function formatMessage($message)
    {
        // Ensure message object exists
        if (!$message) {
            return null;
        }

        return [
            'id' => $message->id ?? null,
            'profile_id' => $message->profile_id ?? null,
            'message_id' => $message->message_id ?? '',
            'type' => $message->type ?? 'text',
            'content' => $message->content ?? '',
            'attachments' => $message->attachments?->map(fn($a) => [
                'id' => $a->id ?? null,
                'filename' => $a->filename ?? '',
                'mime_type' => $a->mime_type ?? '',
                'file_size' => $a->file_size ?? 0,
                'file_url' => $a->file_url ?? '',
                'whatsapp_media_url' => $a->file_url ?? '',
                'is_image' => $a->isImage() ?? false,
                'is_video' => $a->isVideo() ?? false,
                'is_audio' => $a->isAudio() ?? false,
                'is_document' => $a->isDocument() ?? false,
            ])->toArray() ?? [],
            'has_attachments' => $message->attachments?->count() > 0 ?? false,
            'is_outgoing' => $message->is_outgoing ?? false,
            'status' => $message->status ?? 'pending',
            'sent_at' => $message->sent_at ?? now(),
            'formatted_time' => $message->sent_at ? $message->sent_at->format('g:i A') : now()->format('g:i A'),
            'formatted_date' => $message->sent_at ? $message->sent_at->format('Y-m-d') : now()->format('Y-m-d')
        ];
    }

    private function updateSingleConversation($profileId)
    {
        try {

            if (!$this->whatsappSettings) {
                return;
            }

            $updatedConversation = WhatsAppProfile::select([
                'profiles.*',
                'last_messages.content as last_message_content',
                'last_messages.type as last_message_type',
                'last_messages.sent_at as last_message_time',
                DB::raw('COUNT(CASE WHEN messages.status != "read" AND messages.is_outgoing = 0 THEN 1 END) as unread_count')
            ])
                ->where('profiles.id', $profileId)
                ->where('profiles.whatsapp_settings_id', $this->whatsappSettings->id)
                ->where('is_client', false)
                ->leftJoin('messages', 'profiles.id', '=', 'messages.profile_id')
                ->leftJoin(
                    'messages as last_messages',
                    fn($join) =>
                    $join->on('profiles.id', '=', 'last_messages.profile_id')
                        ->whereRaw('last_messages.id = (SELECT MAX(id) FROM messages WHERE messages.profile_id = profiles.id)')
                )
                ->groupBy([
                    'profiles.id',
                    'profiles.whatsapp_id',
                    'profiles.profile_name',
                    'profiles.phone_number',
                    'profiles.profile_picture_url',
                    'profiles.is_online',
                    'profiles.last_seen',
                    'last_messages.content',
                    'last_messages.type',
                    'last_messages.sent_at'
                ])
                ->first();

            if ($updatedConversation) {
                $conversationData = $this->formatConversation($updatedConversation);

                // Skip if conversation data is null
                if (!$conversationData) {
                    return;
                }

                // Ensure conversations is an array
                if (!is_array($this->conversations)) {
                    $this->conversations = [];
                }

                $index = array_search($profileId, array_column($this->conversations, 'id'));

                if ($index !== false) {
                    // Update existing conversation
                    $this->conversations[$index] = $conversationData;
                } else {
                    // Add new conversation at the beginning
                    array_unshift($this->conversations, $conversationData);
                }

                // Sort conversations by latest message time (newest first)
                if (!empty($this->conversations)) {
                    usort(
                        $this->conversations,
                        function ($a, $b) {
                            $timeA = $a['last_message_timestamp'] ?? '';
                            $timeB = $b['last_message_timestamp'] ?? '';

                            // If both have times, compare them
                            if ($timeA && $timeB) {
                                return $timeB - $timeA;
                            }

                            // If only one has time, prioritize the one with time
                            if ($timeA && !$timeB) return -1;
                            if (!$timeA && $timeB) return 1;

                            // If neither has time, keep original order
                            return 0;
                        }

                    );
                }
                // dd($this->conversations);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error('Error updating single conversation: ' . $e->getMessage(), [
                'profile_id' => $profileId,
                'user_id' => Auth::id(),
                'trace' => $e->getTraceAsString()
            ]);

            // Fallback to reloading all conversations
            $this->loadConversations();
        }
    }


    public function render()
    {
        return view('livewire.social-bots.whatsapp.whatsapp-main')->layout(
            'components.base-layout',
            [
                'isSidebarOpen' => 'true',
                'title' => 'Whatsapp Bot',
                'hasMinSidebar' => 'true'
            ]
        );
    }
}
