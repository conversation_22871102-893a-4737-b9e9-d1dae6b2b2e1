<?php

namespace App\Helpers;

use App\Services\BusinessServices\ClinicService;
use Illuminate\Support\Facades\Log;

class ClinicQueryHelper
{
    protected $clinicService;

    public function __construct()
    {
        $this->clinicService = new ClinicService();
    }

    /**
     * البحث عن عيادة معينة بالاسم للمستخدم المحدد
     */
    public function findClinicByName(string $clinicName, int $userId): array
    {
        try {
            $clinics = $this->clinicService->searchClinicsByNameForUser($clinicName, $userId);
            
            if ($clinics->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد عيادة بهذا الاسم: {$clinicName}"
                ];
            }

            $clinicInfo = [];
            foreach ($clinics as $clinic) {
                $workingHours = $this->clinicService->getClinicWorkingHours($clinic->id);
                
                $clinicInfo[] = [
                    'name' => $clinic->name,
                    'description' => $clinic->description ?? 'غير محدد',
                    'opening_time' => $workingHours['opening_time'] ?? 'غير محدد',
                    'closing_time' => $workingHours['closing_time'] ?? 'غير محدد',
                    'is_open' => $this->clinicService->isClinicOpen($clinic->id),
                    'doctors_count' => $clinic->doctors->count(),
                    'services_count' => $clinic->services->count()
                ];
            }

            return [
                'found' => true,
                'clinics' => $clinicInfo,
                'count' => count($clinicInfo)
            ];
        } catch (\Exception $e) {
            Log::error('Error finding clinic by name: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في البحث عن العيادة'
            ];
        }
    }

    /**
     * الحصول على مواعيد عمل عيادة معينة للمستخدم المحدد
     */
    public function getClinicWorkingHours(string $clinicName, int $userId): array
    {
        try {
            $clinicResult = $this->findClinicByName($clinicName, $userId);

            if (!$clinicResult['found']) {
                return [
                    'found' => false,
                    'message' => "لم أجد عيادة بهذا الاسم: {$clinicName}"
                ];
            }

            $clinic = $clinicResult['clinics'][0];

            return [
                'found' => true,
                'clinic_name' => $clinic['name'],
                'opening_time' => $clinic['opening_time'],
                'closing_time' => $clinic['closing_time'],
                'is_open_now' => $clinic['is_open'],
                'status' => $clinic['is_open'] ? 'مفتوحة الآن' : 'مغلقة الآن'
            ];
        } catch (\Exception $e) {
            Log::error('Error getting clinic working hours: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على مواعيد العمل'
            ];
        }
    }

    /**
     * الحصول على أطباء عيادة معينة للمستخدم المحدد
     */
    public function getClinicDoctors(string $clinicName, int $userId): array
    {
        try {
            $clinics = $this->clinicService->searchClinicsByNameForUser($clinicName, $userId);
            
            if ($clinics->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد عيادة بهذا الاسم: {$clinicName}"
                ];
            }

            $clinic = $clinics->first();
            $doctors = $clinic->doctors;

            if ($doctors->isEmpty()) {
                return [
                    'found' => true,
                    'clinic_name' => $clinic->name,
                    'doctors' => [],
                    'message' => "لا يوجد أطباء في عيادة {$clinic->name} حالياً"
                ];
            }

            $doctorsList = [];
            foreach ($doctors as $doctor) {
                $doctorsList[] = [
                    'name' => "د. {$doctor->first_name} {$doctor->last_name}",
                    'specialization' => $doctor->specialization,
                    'available' => $doctor->is_available ? 'متاح' : 'غير متاح',
                    'experience' => $doctor->experience ?? 'غير محدد'
                ];
            }

            return [
                'found' => true,
                'clinic_name' => $clinic->name,
                'doctors' => $doctorsList,
                'count' => count($doctorsList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting clinic doctors: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على أطباء العيادة'
            ];
        }
    }

    /**
     * الحصول على خدمات عيادة معينة للمستخدم المحدد
     */
    public function getClinicServices(string $clinicName, int $userId): array
    {
        try {
            $clinics = $this->clinicService->searchClinicsByNameForUser($clinicName, $userId);
            
            if ($clinics->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد عيادة بهذا الاسم: {$clinicName}"
                ];
            }

            $clinic = $clinics->first();
            $services = $this->clinicService->getClinicServices($clinic->id);

            $servicesList = [];
            foreach ($services as $service) {
                $servicesList[] = [
                    'name' => $service->name,
                    'description' => $service->description ?? 'غير محدد',
                    'price' => $service->price ? $service->price . ' ريال' : 'غير محدد'
                ];
            }

            return [
                'found' => true,
                'clinic_name' => $clinic->name,
                'services' => $servicesList,
                'count' => count($servicesList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting clinic services: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على خدمات العيادة'
            ];
        }
    }

    /**
     * الحصول على عروض عيادة معينة للمستخدم المحدد
     */
    public function getClinicOffers(string $clinicName, int $userId): array
    {
        try {
            $clinics = $this->clinicService->searchClinicsByNameForUser($clinicName, $userId);
            
            if ($clinics->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد عيادة بهذا الاسم: {$clinicName}"
                ];
            }

            $clinic = $clinics->first();
            $offers = $this->clinicService->getActiveClinicOffers($clinic->id);

            $offersList = [];
            foreach ($offers as $offer) {
                $offersList[] = [
                    'title' => $offer->title,
                    'description' => $offer->description ?? 'غير محدد',
                    'start_date' => $offer->start_date->format('Y-m-d'),
                    'end_date' => $offer->end_date->format('Y-m-d')
                ];
            }

            return [
                'found' => true,
                'clinic_name' => $clinic->name,
                'offers' => $offersList,
                'count' => count($offersList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting clinic offers: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على عروض العيادة'
            ];
        }
    }

    /**
     * الحصول على جميع العيادات المفتوحة للمستخدم المحدد
     */
    public function getOpenClinics(int $userId): array
    {
        try {
            $clinics = $this->clinicService->getOpenClinicsForUser($userId);
            
            $clinicsList = [];
            foreach ($clinics as $clinic) {
                $workingHours = $this->clinicService->getClinicWorkingHours($clinic->id);
                
                $clinicsList[] = [
                    'name' => $clinic->name,
                    'opening_time' => $workingHours['opening_time'] ?? 'غير محدد',
                    'closing_time' => $workingHours['closing_time'] ?? 'غير محدد'
                ];
            }

            return [
                'clinics' => $clinicsList,
                'count' => count($clinicsList)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting open clinics: ' . $e->getMessage());
            return [
                'clinics' => [],
                'count' => 0
            ];
        }
    }
}
