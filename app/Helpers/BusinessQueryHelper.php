<?php

namespace App\Helpers;

use App\Services\BusinessServices\BusinessService;
use Illuminate\Support\Facades\Log;

class BusinessQueryHelper
{
    protected $businessService;

    public function __construct()
    {
        $this->businessService = new BusinessService();
    }

    /**
     * الحصول على معلومات الاتصال للعمل الخاص بالمستخدم
     */
    public function getBusinessContactInfo(int $userId): array
    {
        try {
            $business = $this->businessService->getBusinessByUserId($userId);

            if (!$business) {
                return [
                    'found' => false,
                    'message' => 'لا توجد معلومات اتصال متاحة لهذا المستخدم'
                ];
            }

            $businessInfo = [
                'name' => $business->name,
                'phone' => $business->phone ?? 'غير محدد',
                'email' => $business->email ?? 'غير محدد',
                'address' => $business->address ?? 'غير محدد',
                'city' => $business->city ?? 'غير محدد',
                'website' => $business->website ?? 'غير محدد'
            ];

            return [
                'found' => true,
                'business' => $businessInfo
            ];
        } catch (\Exception $e) {
            Log::error('Error getting business contact info: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على معلومات الاتصال'
            ];
        }
    }

    /**
     * البحث عن عمل معين بالاسم
     */
    public function findBusinessByName(string $businessName): array
    {
        try {
            $businesses = $this->businessService->searchBusinessesByName($businessName);
            
            if ($businesses->isEmpty()) {
                return [
                    'found' => false,
                    'message' => "لم أجد عمل بهذا الاسم: {$businessName}"
                ];
            }

            $businessInfo = [];
            foreach ($businesses as $business) {
                $businessInfo[] = [
                    'name' => $business->name,
                    'description' => $business->description ?? 'غير محدد',
                    'phone' => $business->phone ?? 'غير محدد',
                    'email' => $business->email ?? 'غير محدد',
                    'address' => $business->address ?? 'غير محدد',
                    'city' => $business->city ?? 'غير محدد',
                    'website' => $business->website ?? 'غير محدد',
                    'clinics_count' => $business->clinics->count(),
                    'doctors_count' => $business->doctors->count()
                ];
            }

            return [
                'found' => true,
                'businesses' => $businessInfo,
                'count' => count($businessInfo)
            ];
        } catch (\Exception $e) {
            Log::error('Error finding business by name: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في البحث عن العمل'
            ];
        }
    }

    /**
     * الحصول على عنوان العمل
     */
    public function getBusinessAddress(string $businessName = null): array
    {
        try {
            if ($businessName) {
                $result = $this->findBusinessByName($businessName);
                if ($result['found']) {
                    $business = $result['businesses'][0];
                    return [
                        'found' => true,
                        'business_name' => $business['name'],
                        'address' => $business['address'],
                        'city' => $business['city']
                    ];
                } else {
                    return $result;
                }
            } else {
                // إذا لم يحدد اسم العمل، أعطي معلومات جميع الأعمال
                $businesses = $this->businessService->getAllBusinesses();
                
                $addressInfo = [];
                foreach ($businesses as $business) {
                    $addressInfo[] = [
                        'name' => $business->name,
                        'address' => $business->address ?? 'غير محدد',
                        'city' => $business->city ?? 'غير محدد'
                    ];
                }

                return [
                    'found' => true,
                    'addresses' => $addressInfo,
                    'count' => count($addressInfo)
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error getting business address: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على العنوان'
            ];
        }
    }

    /**
     * الحصول على رقم هاتف العمل
     */
    public function getBusinessPhone(string $businessName = null): array
    {
        try {
            if ($businessName) {
                $result = $this->findBusinessByName($businessName);
                if ($result['found']) {
                    $business = $result['businesses'][0];
                    return [
                        'found' => true,
                        'business_name' => $business['name'],
                        'phone' => $business['phone']
                    ];
                } else {
                    return $result;
                }
            } else {
                // إذا لم يحدد اسم العمل، أعطي أرقام جميع الأعمال
                $businesses = $this->businessService->getAllBusinesses();
                
                $phoneInfo = [];
                foreach ($businesses as $business) {
                    $phoneInfo[] = [
                        'name' => $business->name,
                        'phone' => $business->phone ?? 'غير محدد'
                    ];
                }

                return [
                    'found' => true,
                    'phones' => $phoneInfo,
                    'count' => count($phoneInfo)
                ];
            }
        } catch (\Exception $e) {
            Log::error('Error getting business phone: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على رقم الهاتف'
            ];
        }
    }

    /**
     * الحصول على معلومات شاملة عن العمل
     */
    public function getBusinessOverview(): array
    {
        try {
            $businesses = $this->businessService->getAllBusinesses();
            
            $overview = [];
            foreach ($businesses as $business) {
                $overview[] = [
                    'name' => $business->name,
                    'description' => $business->description ?? 'غير محدد',
                    'total_clinics' => $business->clinics->count(),
                    'total_doctors' => $business->doctors->count(),
                    'contact' => [
                        'phone' => $business->phone ?? 'غير محدد',
                        'email' => $business->email ?? 'غير محدد',
                        'address' => $business->address ?? 'غير محدد'
                    ]
                ];
            }

            return [
                'found' => true,
                'overview' => $overview,
                'count' => count($overview)
            ];
        } catch (\Exception $e) {
            Log::error('Error getting business overview: ' . $e->getMessage());
            return [
                'found' => false,
                'message' => 'حدث خطأ في الحصول على نظرة عامة'
            ];
        }
    }
}
