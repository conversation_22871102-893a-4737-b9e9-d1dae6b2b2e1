<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ParseBroadcastingFormData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only process POST requests to broadcasting auth endpoint
        if ($request->isMethod('POST') && $request->is('broadcasting/auth')) {
            $contentType = $request->header('Content-Type');
            
            // If the request contains form data, parse it and merge into request
            if ($contentType && str_contains($contentType, 'application/x-www-form-urlencoded')) {
                $rawContent = $request->getContent();
                
                if (!empty($rawContent)) {
                    parse_str($rawContent, $formData);
                    
                    // Merge the parsed form data into the request
                    $request->merge($formData);
                }
            }
        }

        return $next($request);
    }
}
