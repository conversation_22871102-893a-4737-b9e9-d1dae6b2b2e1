<?php

namespace App\Services\SocialMediaServices;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Models\WhatsApp\WhatsAppMessage;
use App\Services\BusinessServices\BusinessService;

class WhatsAppCacheService
{
    // Cache TTL constants
    const BUSINESS_CACHE_TTL = 72000; // 20 hours in seconds
    const MESSAGES_CACHE_TTL = 300;   // 5 minutes in seconds
    const MAX_CACHED_MESSAGES = 20;   // Maximum messages to keep in cache

    // Cache key prefixes
    const BUSINESS_KEY_PREFIX = 'whatsapp:business:';
    const MESSAGES_KEY_PREFIX = 'whatsapp:messages:';

    /**
     * Get business data from cache or database
     * Cache for 20 hours using user_id as key
     */
    public static function getBusinessData($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;

            // Try to get from cache first
            $cachedData = Cache::get($cacheKey);

            if ($cachedData) {
                Log::info("✅ Business data retrieved from cache", ['user_id' => $userId]);
                return $cachedData;
            }

            // Not in cache, get from database
            Log::info("📊 Business data not in cache, fetching from database", ['user_id' => $userId]);

            $businessService = new BusinessService();
            $business = $businessService->getBusinessByUserId($userId);

            if (!$business) {
                Log::error('Business not found for user', ['user_id' => $userId]);
                return null;
            }

            // Cache ALL business table data - AI will pick what it needs
            $businessData = [
                'business_id' => $business->id,
                'business_name' => $business->name,
                'business_description' => $business->description ?? '',
                'business_website' => $business->website ?? '',
                'business_email' => $business->email ?? '',
                'business_phone' => $business->phone ?? '',
                'business_address' => $business->address ?? '',
                'business_city' => $business->city ?? '',
                'business_state' => $business->state ?? '',
                'business_country' => $business->country ?? '',
                'business_logo' => $business->logo ?? '',
                'google_map_link' => $business->google_map_link ?? '',
                'google_review_link' => $business->google_review_link ?? '',
                'facebook_page_link' => $business->facebook_page_link ?? '',
                'instagram_page_link' => $business->instagram_page_link ?? '',
                'twitter_page_link' => $business->twitter_page_link ?? '',
                'linkedin_page_link' => $business->linkedin_page_link ?? '',
                'youtube_channel_link' => $business->youtube_channel_link ?? '',
                'snapchat_page_link' => $business->snapchat_page_link ?? '',
                'tiktok_page_link' => $business->tiktok_page_link ?? '',
                'whatsapp_number' => $business->whatsapp_number ?? '',
                'telegram_username' => $business->telegram_username ?? '',
                'salla_store_url' => $business->salla_store_url ?? '',
                'cached_at' => now()->toISOString()
            ];

            // Store in cache for 20 hours
            Cache::put($cacheKey, $businessData, self::BUSINESS_CACHE_TTL);

            Log::info("💾 Business data cached successfully", [
                'user_id' => $userId,
                'ttl_hours' => self::BUSINESS_CACHE_TTL / 3600
            ]);

            return $businessData;

        } catch (\Exception $e) {
            Log::error('Error in getBusinessData cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * Get message history from cache or database
     * Cache for 5 minutes using whatsapp_id as key
     * Maintains rolling 20-message limit
     */
    public static function getMessageHistory($whatsappId, $profileId = null)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;

            // Try to get from cache first
            $cachedMessages = Cache::get($cacheKey);

            if ($cachedMessages) {
                // Extend cache TTL on access (rolling 5 minutes)
                Cache::put($cacheKey, $cachedMessages, self::MESSAGES_CACHE_TTL);

                Log::info("✅ Message history retrieved from cache", [
                    'whatsapp_id' => $whatsappId,
                    'message_count' => count($cachedMessages)
                ]);

                return $cachedMessages;
            }

            // Not in cache, get from database if profile_id provided
            if ($profileId) {
                return self::loadMessageHistoryFromDatabase($whatsappId, $profileId);
            }

            return [];

        } catch (\Exception $e) {
            Log::error('Error in getMessageHistory cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'profile_id' => $profileId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Load message history from database and cache it
     * Called when cache is empty and we need to initialize it
     */
    public static function loadMessageHistoryFromDatabase($whatsappId, $profileId)
    {
        try {
            Log::info("📊 Message history not in cache, fetching from database", [
                'whatsapp_id' => $whatsappId,
                'profile_id' => $profileId
            ]);

            $messages = WhatsAppMessage::where('profile_id', $profileId)
                ->orderBy('sent_at', 'desc')
                ->limit(self::MAX_CACHED_MESSAGES)
                ->get()
                ->reverse() // Reverse to get chronological order
                ->map(function($message) {
                    return [
                        'id' => $message->id,
                        'content' => $message->content,
                        'type' => $message->type,
                        'is_outgoing' => $message->is_outgoing,
                        'sent_at' => $message->sent_at->toISOString()
                    ];
                })
                ->toArray();

            // Cache the messages
            self::cacheMessages($whatsappId, $messages);

            return $messages;

        } catch (\Exception $e) {
            Log::error('Error loading message history from database: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'profile_id' => $profileId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Add new message to cache (append to conversation)
     * Maintains rolling conversation history with message limit
     * Each WhatsApp ID has its own conversation cache
     */
    public static function addMessageToCache($whatsappId, $messageData)
    {
        try {
            // Get current conversation from cache (using whatsappId as key)
            $currentMessages = self::getMessageHistory($whatsappId);

            // Prepare new message for cache
            $newMessage = [
                'id' => $messageData['id'] ?? null,
                'content' => $messageData['content'] ?? '',
                'type' => $messageData['type'] ?? 'text',
                'is_outgoing' => $messageData['is_outgoing'] ?? false,
                'role' => $messageData['is_outgoing'] ? 'assistant' : 'user', // For AI context
                'sent_at' => $messageData['sent_at'] ?? now()->toISOString(),
                'cached_at' => now()->toISOString()
            ];

            // Append new message to conversation
            $currentMessages[] = $newMessage;

            // Maintain rolling conversation limit (keep only recent messages)
            if (count($currentMessages) > self::MAX_CACHED_MESSAGES) {
                $currentMessages = array_slice($currentMessages, -self::MAX_CACHED_MESSAGES);

                Log::info("🔄 Conversation trimmed to maintain limit", [
                    'whatsapp_id' => $whatsappId,
                    'max_messages' => self::MAX_CACHED_MESSAGES
                ]);
            }

            // Save updated conversation to cache
            self::cacheMessages($whatsappId, $currentMessages);

            Log::info("💬 Message appended to conversation cache", [
                'whatsapp_id' => $whatsappId,
                'total_messages' => count($currentMessages),
                'message_type' => $newMessage['type'],
                'is_outgoing' => $newMessage['is_outgoing']
            ]);

            return $currentMessages; // Return updated conversation

        } catch (\Exception $e) {
            Log::error('Error appending message to conversation cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'message_data' => $messageData,
                'trace' => $e->getTraceAsString()
            ]);

            return []; // Return empty array on error
        }
    }

    /**
     * Get conversation formatted for AI context
     * Returns messages in a format suitable for AI processing
     */
    public static function getConversationForAI($whatsappId, $profileId = null)
    {
        try {
            $messages = self::getMessageHistory($whatsappId, $profileId);

            if (empty($messages)) {
                return [];
            }

            // Format messages for AI context
            $formattedMessages = array_map(function($message) {
                return [
                    'role' => $message['role'] ?? ($message['is_outgoing'] ? 'assistant' : 'user'),
                    'content' => $message['content'],
                    'timestamp' => $message['sent_at']
                ];
            }, $messages);

            Log::info("🤖 Conversation formatted for AI", [
                'whatsapp_id' => $whatsappId,
                'message_count' => count($formattedMessages)
            ]);

            return $formattedMessages;

        } catch (\Exception $e) {
            Log::error('Error formatting conversation for AI: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * Cache messages with TTL
     */
    private static function cacheMessages($whatsappId, $messages)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;
            Cache::put($cacheKey, $messages, self::MESSAGES_CACHE_TTL);

            Log::info("💾 Conversation cached successfully", [
                'whatsapp_id' => $whatsappId,
                'message_count' => count($messages),
                'ttl_minutes' => self::MESSAGES_CACHE_TTL / 60,
                'cache_key' => $cacheKey
            ]);

        } catch (\Exception $e) {
            Log::error('Error caching conversation: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Clear business cache for specific user (basic data only)
     */
    public static function clearBusinessCache($userId)
    {
        try {
            $cacheKey = self::BUSINESS_KEY_PREFIX . $userId;
            Cache::forget($cacheKey);

            Log::info("🗑️ Business cache cleared", ['user_id' => $userId]);

        } catch (\Exception $e) {
            Log::error('Error clearing business cache: ' . $e->getMessage(), [
                'user_id' => $userId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Clear conversation cache for specific WhatsApp ID
     */
    public static function clearMessageCache($whatsappId)
    {
        try {
            $cacheKey = self::MESSAGES_KEY_PREFIX . $whatsappId;
            Cache::forget($cacheKey);

            Log::info("🗑️ Conversation cache cleared", ['whatsapp_id' => $whatsappId]);

        } catch (\Exception $e) {
            Log::error('Error clearing conversation cache: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get conversation statistics for a specific WhatsApp ID
     */
    public static function getConversationStats($whatsappId)
    {
        try {
            $messages = self::getMessageHistory($whatsappId);

            if (empty($messages)) {
                return [
                    'total_messages' => 0,
                    'user_messages' => 0,
                    'assistant_messages' => 0,
                    'last_message_at' => null,
                    'conversation_started_at' => null
                ];
            }

            $userMessages = array_filter($messages, fn($msg) => !$msg['is_outgoing']);
            $assistantMessages = array_filter($messages, fn($msg) => $msg['is_outgoing']);

            return [
                'total_messages' => count($messages),
                'user_messages' => count($userMessages),
                'assistant_messages' => count($assistantMessages),
                'last_message_at' => end($messages)['sent_at'] ?? null,
                'conversation_started_at' => $messages[0]['sent_at'] ?? null,
                'cache_key' => self::MESSAGES_KEY_PREFIX . $whatsappId
            ];

        } catch (\Exception $e) {
            Log::error('Error getting conversation stats: ' . $e->getMessage(), [
                'whatsapp_id' => $whatsappId,
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }



    /**
     * Get cache statistics (basic data only)
     */
    public static function getCacheStats()
    {
        try {
            // Note: Cache facade doesn't have keys() method like Redis
            // This is a simplified version for database cache
            return [
                'business_cache_count' => 'N/A (using database cache)',
                'message_cache_count' => 'N/A (using database cache)',
                'total_cache_keys' => 'N/A (using database cache)'
            ];

        } catch (\Exception $e) {
            Log::error('Error getting cache stats: ' . $e->getMessage());
            return [
                'business_cache_count' => 0,
                'message_cache_count' => 0,
                'total_cache_keys' => 0
            ];
        }
    }
}
