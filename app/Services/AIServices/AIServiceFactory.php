<?php

namespace App\Services\AIServices;

use App\Models\AiProvider;
use Illuminate\Support\Facades\Log;

class AIServiceFactory
{
    /**
     * Create an AI service instance based on provider name
     */
    public static function create(string $providerName): BaseAIService
    {
        switch (strtolower($providerName)) {
            case 'openai':
                return new OpenAIService();
            
            case 'google':
                return new GoogleService();
            
            case 'openrouter':
                return new OpenRouterService();
            
            case 'requesty':
                return new RequestyService();
            
            case 'hugging face':
                return new HuggingFaceService();

            case 'deepseek':
                return new DeepSeekService();

            case 'togather ai':
                return new TogetherAIService();

            case 'imagerouter':
                return new ImageRouterService();

            default:
                throw new \InvalidArgumentException("Unsupported AI provider: {$providerName}");
        }
    }

    /**
     * Create an AI service instance by provider ID
     */
    public static function createById(int $providerId): BaseAIService
    {
        $provider = AiProvider::findOrFail($providerId);
        return self::create($provider->name);
    }

    /**
     * Get all available AI service providers
     */
    public static function getAvailableProviders(): array
    {
        return AiProvider::where('status', 'active')->get()->toArray();
    }

    /**
     * Check if a provider is supported
     */
    public static function isSupported(string $providerName): bool
    {
        $supportedProviders = [
            'openai',
            'google',
            'google gemini',
            'openrouter',
            'requesty',
            'hugging face',
            'deepseek',
            'togather ai',
            'imagerouter'
        ];

        return in_array(strtolower($providerName), $supportedProviders);
    }

    /**
     * Get the default provider service
     */
    public static function getDefault(): BaseAIService
    {
        $defaultProvider = AiProvider::where('status', 'active')->first();
        
        if (!$defaultProvider) {
            throw new \Exception('No active AI providers found');
        }

        return self::create($defaultProvider->name);
    }

    /**
     * Create multiple services for batch operations
     */
    public static function createMultiple(array $providerNames): array
    {
        $services = [];
        
        foreach ($providerNames as $providerName) {
            try {
                $services[$providerName] = self::create($providerName);
            } catch (\Exception $e) {
                // Log error but continue with other providers
                Log::warning("Failed to create service for provider: {$providerName}", [
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $services;
    }

    /**
     * Get services that support specific capabilities
     */
    public static function getServicesByCapability(string $capability): array
    {
        $providers = AiProvider::whereHas('models.capabilities', function ($query) use ($capability) {
            $query->where('name', $capability);
        })->where('status', 'active')->get();

        $services = [];
        foreach ($providers as $provider) {
            try {
                $services[] = self::create($provider->name);
            } catch (\Exception $e) {
                Log::warning("Failed to create service for provider: {$provider->name}", [
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $services;
    }

    /**
     * Get chat-capable services
     */
    public static function getChatServices(): array
    {
        return self::getServicesByCapability('chat');
    }

    /**
     * Get image generation capable services
     */
    public static function getImageGenerationServices(): array
    {
        return self::getServicesByCapability('image_generates');
    }

    /**
     * Get image editing capable services
     */
    public static function getImageEditingServices(): array
    {
        return self::getServicesByCapability('image_edits');
    }

    /**
     * Get video generation capable services
     */
    public static function getVideoGenerationServices(): array
    {
        return self::getServicesByCapability('video_generating');
    }
}
