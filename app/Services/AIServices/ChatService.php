<?php

namespace App\Services\AIServices;

use App\Services\BusinessServices\ClinicService;
use App\Services\BusinessServices\DoctorService;
use App\Services\BusinessServices\AppointmentService;
use App\Services\BusinessServices\BusinessService;
use App\Services\AIServices\AIServiceFactory;
use App\Helpers\DoctorQueryHelper;
use App\Helpers\ClinicQueryHelper;
use App\Helpers\BusinessQueryHelper;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ChatService
{

    private $analyzer = [
        'provider' => 'google',
        'model' => 'gemma-3n-e2b-it'
    ];

    private $responder = [
        'provider' => 'google',
        'model' => 'gemini-2.5-flash-lite-preview-06-17'
    ];

    public function __construct()
    {

    }

    /**
     * تحليل طلب العميل وإرجاع JSON للرد
     */
    public function generateResponse(string $userMessage, array $context = []): string
    {
        try {
            // الخطوة الأولى: تحليل طلب العميل بالذكاء الاصطناعي
            $analysis = $this->analyzeCustomerRequest($userMessage, $context);

            // الخطوة الثانية: إنشاء الرد بناءً على التحليل
            $response = $this->generateResponseFromAnalysis($analysis, $context);

            return $response;

        } catch (\Exception $e) {
            Log::error('ChatService Error: ' . $e->getMessage(), [
                'message' => $userMessage,
                'trace' => $e->getTraceAsString()
            ]);
            return 'عذراً، حدث خطأ في النظام. يرجى المحاولة لاحقاً.';
        }
    }

    /**
     * تحليل طلب العميل بالذكاء الاصطناعي
     */
    private function analyzeCustomerRequest(string $userMessage, array $context): array
    {
        $aiService = AIServiceFactory::create($this->analyzer['provider']);

        $analysisPrompt = $this->buildAnalysisPrompt($userMessage);

        $response = $aiService->chat($analysisPrompt, $this->analyzer['model'], [
            'temperature' => 0.3,
            'max_tokens' => 200,
        ]);

        if (isset($response['success']) && $response['success'] === true) {
            $analysisResult = json_decode($response['content'], true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $analysisResult;
            }
        }

        // إذا فشل التحليل، إرجاع تحليل افتراضي
        return [
            'intent' => 'general_inquiry',
            'entity_type' => 'none',
            'entity_name' => '',
            'action' => 'help'
        ];
    }

    /**
     * إنشاء الرد بناءً على التحليل
     */
    private function generateResponseFromAnalysis(array $analysis, array $context): string
    {
        $userId = $context['user_id'] ?? null;
        $businessName = $context['business_name'] ?? 'العيادة';

        // جلب البيانات المطلوبة بناءً على التحليل
        $data = $this->fetchRequiredData($analysis, $userId);

        // إنشاء الرد النهائي
        $aiService = AIServiceFactory::create($this->responder['provider']);
        $responsePrompt = $this->buildResponsePrompt($analysis, $data, $businessName);

        $response = $aiService->chat($responsePrompt, $this->responder['model'], [
            'temperature' => 0.7,
            'max_tokens' => 300,
        ]);

        if (isset($response['success']) && $response['success'] === true) {
            return $response['content'];
        }

        return "مرحباً بك في {$businessName}. كيف يمكنني مساعدتك؟";
    }

    /**
     * بناء prompt لتحليل طلب العميل
     */
    private function buildAnalysisPrompt(string $userMessage): string
    {
        return "حلل هذه الرسالة من العميل وأرجع JSON فقط:

رسالة العميل: \"{$userMessage}\"

أرجع JSON بهذا التنسيق:
{
    \"intent\": \"doctor_inquiry|clinic_inquiry|appointment_inquiry|general_inquiry\",
    \"entity_type\": \"doctor|clinic|appointment|none\",
    \"entity_name\": \"اسم الطبيب أو العيادة إن وجد\",
    \"action\": \"search|info|help|book\"
}

أمثلة:
- \"من اطبائكم في العيون؟\" -> {\"intent\":\"doctor_inquiry\",\"entity_type\":\"doctor\",\"entity_name\":\"العيون\",\"action\":\"search\"}
- \"اريد موعد مع د.أحمد\" -> {\"intent\":\"appointment_inquiry\",\"entity_type\":\"doctor\",\"entity_name\":\"أحمد\",\"action\":\"book\"}
- \"مرحبا\" -> {\"intent\":\"general_inquiry\",\"entity_type\":\"none\",\"entity_name\":\"\",\"action\":\"help\"}";
    }

    /**
     * جلب البيانات المطلوبة بناءً على التحليل
     */
    private function fetchRequiredData(array $analysis, ?int $userId): array
    {
        if (!$userId) {
            return [];
        }

        $data = [];

        switch ($analysis['intent']) {
            case 'doctor_inquiry':
                $data = $this->fetchDoctorData($analysis, $userId);
                break;
            case 'clinic_inquiry':
                $data = $this->fetchClinicData($analysis, $userId);
                break;
            case 'appointment_inquiry':
                $data = $this->fetchAppointmentData($analysis, $userId);
                break;
            default:
                $data = [];
        }

        return $data;
    }

    /**
     * جلب بيانات الأطباء
     */
    private function fetchDoctorData(array $analysis, int $userId): array
    {
        $doctorHelper = new DoctorQueryHelper();

        if (!empty($analysis['entity_name'])) {
            // البحث عن طبيب معين أو تخصص
            $result = $doctorHelper->getDoctorsBySpecialization($analysis['entity_name'], $userId);
            if (!$result['found']) {
                $result = $doctorHelper->findDoctorByName($analysis['entity_name'], $userId);
            }
            return $result;
        }

        // إرجاع الأطباء المتاحين
        return $doctorHelper->getAvailableDoctors($userId, 5);
    }

    /**
     * جلب بيانات العيادات
     */
    private function fetchClinicData(array $analysis, int $userId): array
    {
        $clinicHelper = new ClinicQueryHelper();

        if (!empty($analysis['entity_name'])) {
            return $clinicHelper->findClinicByName($analysis['entity_name'], $userId);
        }

        return $clinicHelper->getOpenClinics($userId);
    }

    /**
     * جلب بيانات المواعيد
     */
    private function fetchAppointmentData(array $analysis, int $userId): array
    {
        // منطق بسيط للمواعيد
        return [
            'message' => 'يمكنك حجز موعد عبر الاتصال بنا أو زيارة العيادة'
        ];
    }

    /**
     * بناء prompt للرد النهائي
     */
    private function buildResponsePrompt(array $analysis, array $data, string $businessName): string
    {
        $prompt = "أنت مساعد ذكي لـ {$businessName}. أجب بطريقة مهذبة ومفيدة.\n\n";
        $prompt .= "تحليل الطلب: " . json_encode($analysis, JSON_UNESCAPED_UNICODE) . "\n\n";

        if (!empty($data)) {
            $prompt .= "البيانات المتاحة: " . json_encode($data, JSON_UNESCAPED_UNICODE) . "\n\n";
        }

        $prompt .= "اكتب رد مناسب باللغة العربية (أقل من 200 كلمة):";

        return $prompt;
    }
}
