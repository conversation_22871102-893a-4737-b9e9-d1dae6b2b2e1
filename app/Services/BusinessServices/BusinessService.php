<?php

namespace App\Services\BusinessServices;

use App\Models\Business;
use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\Customer;
use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BusinessService
{
    /**
     * Get all businesses
     */
    public function getAllBusinesses(): Collection
    {
        return Business::with(['user', 'clinics', 'doctors', 'customers'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get business by ID with relationships
     */
    public function getBusinessById(int $businessId): ?Business
    {
        return Business::with(['user', 'clinics', 'doctors', 'customers'])
            ->find($businessId);
    }

    /**
     * Get businesses by user ID
     */
    public function getBusinessesByUser(int $userId): Collection
    {
        return Business::with(['clinics', 'doctors', 'customers'])
            ->where('user_id', $userId)
            ->orderBy('name')
            ->get();
    }

    /**
     * Search businesses by name, description, or location
     */
    public function searchBusinesses(string $searchTerm): Collection
    {
        return Business::with(['user', 'clinics'])
            ->where(function ($query) use ($searchTerm) {
                $query->where('name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('description', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('city', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('state', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('country', 'LIKE', "%{$searchTerm}%");
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get business full information
     */
    public function getBusinessFullInfo(int $businessId): array
    {
        $business = $this->getBusinessById($businessId);
        
        if (!$business) {
            return [];
        }

        return [
            'basic_info' => [
                'id' => $business->id,
                'name' => $business->name,
                'description' => $business->description,
                'website' => $business->website,
                'email' => $business->email,
                'phone' => $business->phone,
                'logo' => $business->logo,
            ],
            'location' => [
                'address' => $business->address,
                'city' => $business->city,
                'state' => $business->state,
                'country' => $business->country,
                'google_map_link' => $business->google_map_link,
            ],
            'owner_info' => [
                'name' => $business->user->name,
                'email' => $business->user->email,
                'avatar_url' => $business->user->avatar_url,
            ],
            'social_media' => [
                'facebook_page_link' => $business->facebook_page_link,
                'instagram_page_link' => $business->instagram_page_link,
                'twitter_page_link' => $business->twitter_page_link,
                'linkedin_page_link' => $business->linkedin_page_link,
                'youtube_channel_link' => $business->youtube_channel_link,
                'snapchat_page_link' => $business->snapchat_page_link,
                'tiktok_page_link' => $business->tiktok_page_link,
                'whatsapp_number' => $business->whatsapp_number,
                'telegram_username' => $business->telegram_username,
            ],
            'online_presence' => [
                'google_review_link' => $business->google_review_link,
                'salla_store_url' => $business->salla_store_url,
            ],
        ];
    }

    /**
     * Get business statistics
     */
    public function getBusinessStats(int $businessId): array
    {
        $business = $this->getBusinessById($businessId);
        
        if (!$business) {
            return [];
        }

        return [
            'total_clinics' => $business->clinics()->count(),
            'open_clinics' => $business->clinics()->where('is_open', true)->count(),
            'total_doctors' => $business->doctors()->count(),
            'available_doctors' => $business->doctors()->where('is_available', true)->count(),
            'total_customers' => $business->customers()->count(),
            'loyal_customers' => $business->customers()->where('is_loyal_customer', true)->count(),
            'total_appointments' => $this->getBusinessAppointmentsCount($businessId),
            'pending_appointments' => $this->getBusinessAppointmentsByStatus($businessId, 'pending')->count(),
            'confirmed_appointments' => $this->getBusinessAppointmentsByStatus($businessId, 'confirmed')->count(),
            'completed_appointments' => $this->getBusinessAppointmentsByStatus($businessId, 'completed')->count(),
            'cancelled_appointments' => $this->getBusinessAppointmentsByStatus($businessId, 'cancelled')->count(),
            'today_appointments' => $this->getTodayBusinessAppointments($businessId)->count(),
            'this_week_appointments' => $this->getWeekBusinessAppointments($businessId)->count(),
            'this_month_appointments' => $this->getMonthBusinessAppointments($businessId)->count(),
        ];
    }

    /**
     * Get business clinics
     */
    public function getBusinessClinics(int $businessId): Collection
    {
        return Clinic::with(['business', 'doctors'])
            ->where('business_id', $businessId)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get business doctors
     */
    public function getBusinessDoctors(int $businessId): Collection
    {
        return Doctor::with(['business', 'clinic'])
            ->where('business_id', $businessId)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get business customers
     */
    public function getBusinessCustomers(int $businessId): Collection
    {
        return Customer::where('business_id', $businessId)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get business appointments count
     */
    private function getBusinessAppointmentsCount(int $businessId): int
    {
        return Appointment::where('business_id', $businessId)->count();
    }

    /**
     * Get business appointments by status
     */
    private function getBusinessAppointmentsByStatus(int $businessId, string $status): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('business_id', $businessId)
            ->where('status', $status)
            ->get();
    }

    /**
     * Get today's business appointments
     */
    private function getTodayBusinessAppointments(int $businessId): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('business_id', $businessId)
            ->whereDate('appointment_date', Carbon::today())
            ->get();
    }

    /**
     * Get this week's business appointments
     */
    private function getWeekBusinessAppointments(int $businessId): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('business_id', $businessId)
            ->whereBetween('appointment_date', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])
            ->get();
    }

    /**
     * Get this month's business appointments
     */
    private function getMonthBusinessAppointments(int $businessId): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('business_id', $businessId)
            ->whereMonth('appointment_date', Carbon::now()->month)
            ->whereYear('appointment_date', Carbon::now()->year)
            ->get();
    }

    /**
     * Get businesses by location (city, state, or country)
     */
    public function getBusinessesByLocation(string $location): Collection
    {
        return Business::with(['user', 'clinics'])
            ->where(function ($query) use ($location) {
                $query->where('city', 'LIKE', "%{$location}%")
                    ->orWhere('state', 'LIKE', "%{$location}%")
                    ->orWhere('country', 'LIKE', "%{$location}%");
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get businesses with most clinics
     */
    public function getBusinessesWithMostClinics(int $limit = 10): Collection
    {
        return Business::with(['user'])
            ->withCount('clinics')
            ->orderBy('clinics_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get businesses with most doctors
     */
    public function getBusinessesWithMostDoctors(int $limit = 10): Collection
    {
        return Business::with(['user'])
            ->withCount('doctors')
            ->orderBy('doctors_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get business contact information
     */
    public function getBusinessContactInfo(int $businessId): array
    {
        $business = $this->getBusinessById($businessId);
        
        if (!$business) {
            return [];
        }

        return [
            'name' => $business->name,
            'email' => $business->email,
            'phone' => $business->phone,
            'address' => $business->address,
            'city' => $business->city,
            'state' => $business->state,
            'country' => $business->country,
            'website' => $business->website,
            'whatsapp_number' => $business->whatsapp_number,
            'telegram_username' => $business->telegram_username,
        ];
    }

    /**
     * Get business social media links
     */
    public function getBusinessSocialMediaLinks(int $businessId): array
    {
        $business = $this->getBusinessById($businessId);
        
        if (!$business) {
            return [];
        }

        return [
            'facebook' => $business->facebook_page_link,
            'instagram' => $business->instagram_page_link,
            'twitter' => $business->twitter_page_link,
            'linkedin' => $business->linkedin_page_link,
            'youtube' => $business->youtube_channel_link,
            'snapchat' => $business->snapchat_page_link,
            'tiktok' => $business->tiktok_page_link,
            'google_reviews' => $business->google_review_link,
            'salla_store' => $business->salla_store_url,
        ];
    }

    /**
     * البحث عن عمل بالاسم
     */
    public function searchBusinessesByName(string $businessName)
    {
        return Business::where('name', 'LIKE', "%{$businessName}%")
            ->with(['clinics', 'doctors'])
            ->get();
    }

    /**
     * الحصول على العمل الخاص بالمستخدم
     */
    public function getBusinessByUserId(int $userId)
    {
        return Business::where('user_id', $userId)
            ->with(['clinics', 'doctors'])
            ->first();
    }
}
