<?php

namespace App\Services\BusinessServices;

use App\Models\Appointment;
use App\Models\Doctor;
use App\Models\Clinic;
use App\Models\Customer;
use App\Models\Business;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AppointmentService
{
    /**
     * Get all appointments with relationships
     */
    public function getAllAppointments(): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic', 'business'])
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointment by ID with relationships
     */
    public function getAppointmentById(int $appointmentId): ?Appointment
    {
        return Appointment::with(['customer', 'doctor', 'clinic', 'business'])
            ->find($appointmentId);
    }

    /**
     * Get appointments by status
     */
    public function getAppointmentsByStatus(string $status): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('status', $status)
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get pending appointments
     */
    public function getPendingAppointments(): Collection
    {
        return $this->getAppointmentsByStatus('pending');
    }

    /**
     * Get confirmed appointments
     */
    public function getConfirmedAppointments(): Collection
    {
        return $this->getAppointmentsByStatus('confirmed');
    }

    /**
     * Get completed appointments
     */
    public function getCompletedAppointments(): Collection
    {
        return $this->getAppointmentsByStatus('completed');
    }

    /**
     * Get cancelled appointments
     */
    public function getCancelledAppointments(): Collection
    {
        return $this->getAppointmentsByStatus('cancelled');
    }

    /**
     * Get appointments for a specific date
     */
    public function getAppointmentsByDate(string $date): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->whereDate('appointment_date', $date)
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get today's appointments
     */
    public function getTodayAppointments(): Collection
    {
        return $this->getAppointmentsByDate(Carbon::today()->toDateString());
    }

    /**
     * Get upcoming appointments (next 7 days)
     */
    public function getUpcomingAppointments(int $days = 7): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('appointment_date', '>=', Carbon::now())
            ->where('appointment_date', '<=', Carbon::now()->addDays($days))
            ->whereIn('status', ['pending', 'confirmed'])
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get appointments by date range
     */
    public function getAppointmentsByDateRange(string $startDate, string $endDate): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->whereBetween('appointment_date', [$startDate, $endDate])
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get appointments by customer ID
     */
    public function getAppointmentsByCustomer(int $customerId): Collection
    {
        return Appointment::with(['doctor', 'clinic'])
            ->where('customer_id', $customerId)
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointments by doctor ID
     */
    public function getAppointmentsByDoctor(int $doctorId): Collection
    {
        return Appointment::with(['customer', 'clinic'])
            ->where('doctor_id', $doctorId)
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointments by clinic ID
     */
    public function getAppointmentsByClinic(int $clinicId): Collection
    {
        return Appointment::with(['customer', 'doctor'])
            ->where('clinic_id', $clinicId)
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointments by business ID
     */
    public function getAppointmentsByBusiness(int $businessId): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('business_id', $businessId)
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Search appointments by customer name, doctor name, or reason
     */
    public function searchAppointments(string $searchTerm): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where(function ($query) use ($searchTerm) {
                $query->where('reason', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('notes', 'LIKE', "%{$searchTerm}%")
                    ->orWhereHas('customer', function ($customerQuery) use ($searchTerm) {
                        $customerQuery->where('first_name', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('last_name', 'LIKE', "%{$searchTerm}%");
                    })
                    ->orWhereHas('doctor', function ($doctorQuery) use ($searchTerm) {
                        $doctorQuery->where('first_name', 'LIKE', "%{$searchTerm}%")
                            ->orWhere('last_name', 'LIKE', "%{$searchTerm}%");
                    });
            })
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointment statistics
     */
    public function getAppointmentStats(): array
    {
        return [
            'total_appointments' => Appointment::count(),
            'pending_appointments' => Appointment::where('status', 'pending')->count(),
            'confirmed_appointments' => Appointment::where('status', 'confirmed')->count(),
            'completed_appointments' => Appointment::where('status', 'completed')->count(),
            'cancelled_appointments' => Appointment::where('status', 'cancelled')->count(),
            'today_appointments' => Appointment::whereDate('appointment_date', Carbon::today())->count(),
            'this_week_appointments' => Appointment::whereBetween('appointment_date', [
                Carbon::now()->startOfWeek(),
                Carbon::now()->endOfWeek()
            ])->count(),
            'this_month_appointments' => Appointment::whereMonth('appointment_date', Carbon::now()->month)
                ->whereYear('appointment_date', Carbon::now()->year)
                ->count(),
            'overdue_appointments' => Appointment::where('appointment_date', '<', Carbon::now())
                ->whereIn('status', ['pending', 'confirmed'])
                ->count(),
        ];
    }

    /**
     * Get appointment statistics by status for a date range
     */
    public function getAppointmentStatsByDateRange(string $startDate, string $endDate): array
    {
        $appointments = $this->getAppointmentsByDateRange($startDate, $endDate);
        
        return [
            'total_appointments' => $appointments->count(),
            'pending_appointments' => $appointments->where('status', 'pending')->count(),
            'confirmed_appointments' => $appointments->where('status', 'confirmed')->count(),
            'completed_appointments' => $appointments->where('status', 'completed')->count(),
            'cancelled_appointments' => $appointments->where('status', 'cancelled')->count(),
            'completion_rate' => $appointments->count() > 0 ? 
                round(($appointments->where('status', 'completed')->count() / $appointments->count()) * 100, 2) : 0,
            'cancellation_rate' => $appointments->count() > 0 ? 
                round(($appointments->where('status', 'cancelled')->count() / $appointments->count()) * 100, 2) : 0,
        ];
    }

    /**
     * Get appointments grouped by date
     */
    public function getAppointmentsGroupedByDate(int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);
        $endDate = Carbon::now();

        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->whereBetween('appointment_date', [$startDate, $endDate])
            ->get()
            ->groupBy(function ($appointment) {
                return Carbon::parse($appointment->appointment_date)->format('Y-m-d');
            })
            ->map(function ($appointments, $date) {
                return [
                    'date' => $date,
                    'total_appointments' => $appointments->count(),
                    'pending' => $appointments->where('status', 'pending')->count(),
                    'confirmed' => $appointments->where('status', 'confirmed')->count(),
                    'completed' => $appointments->where('status', 'completed')->count(),
                    'cancelled' => $appointments->where('status', 'cancelled')->count(),
                    'appointments' => $appointments->values(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Get appointments grouped by status
     */
    public function getAppointmentsGroupedByStatus(): array
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->get()
            ->groupBy('status')
            ->map(function ($appointments, $status) {
                return [
                    'status' => $status,
                    'count' => $appointments->count(),
                    'appointments' => $appointments->values(),
                ];
            })
            ->values()
            ->toArray();
    }

    /**
     * Get overdue appointments (past due and still pending/confirmed)
     */
    public function getOverdueAppointments(): Collection
    {
        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->where('appointment_date', '<', Carbon::now())
            ->whereIn('status', ['pending', 'confirmed'])
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get appointments requiring follow-up (completed appointments from last week)
     */
    public function getAppointmentsRequiringFollowUp(): Collection
    {
        $lastWeekStart = Carbon::now()->subWeek()->startOfWeek();
        $lastWeekEnd = Carbon::now()->subWeek()->endOfWeek();

        return Appointment::with(['customer', 'doctor', 'clinic'])
            ->whereBetween('appointment_date', [$lastWeekStart, $lastWeekEnd])
            ->where('status', 'completed')
            ->orderBy('appointment_date', 'desc')
            ->get();
    }

    /**
     * Get appointment details with full information
     */
    public function getAppointmentFullDetails(int $appointmentId): array
    {
        $appointment = $this->getAppointmentById($appointmentId);
        
        if (!$appointment) {
            return [];
        }

        return [
            'appointment_info' => [
                'id' => $appointment->id,
                'appointment_date' => $appointment->appointment_date,
                'reason' => $appointment->reason,
                'notes' => $appointment->notes,
                'status' => $appointment->status,
                'created_at' => $appointment->created_at,
                'updated_at' => $appointment->updated_at,
            ],
            'customer_info' => [
                'id' => $appointment->customer->id,
                'name' => trim($appointment->customer->first_name . ' ' . $appointment->customer->last_name),
                'phone_number' => $appointment->customer->phone_number,
                'id_number' => $appointment->customer->id_number,
            ],
            'doctor_info' => [
                'id' => $appointment->doctor->id,
                'name' => trim($appointment->doctor->first_name . ' ' . $appointment->doctor->last_name),
                'specialization' => $appointment->doctor->specialization,
            ],
            'clinic_info' => [
                'id' => $appointment->clinic->id,
                'name' => $appointment->clinic->name,
                'phone_number' => $appointment->clinic->phone_number,
                'location' => [
                    'floor' => $appointment->clinic->floor_number,
                    'room' => $appointment->clinic->room_number,
                ],
            ],
        ];
    }
}
