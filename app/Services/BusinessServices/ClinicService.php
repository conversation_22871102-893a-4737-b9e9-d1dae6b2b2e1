<?php

namespace App\Services\BusinessServices;

use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\Appointment;
use App\Models\User;
use App\Models\Offer;
use App\Models\ClinicService as ClinicServiceModel;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ClinicService
{
    /**
     * Get all active clinics
     */
    public function getAllClinics(): Collection
    {
        return Clinic::with(['business', 'doctors', 'appointments'])
            ->where('is_open', true)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinic by ID with relationships
     */
    public function getClinicById(int $clinicId): ?Clinic
    {
        return Clinic::with(['business', 'doctors', 'appointments'])
            ->find($clinicId);
    }

    /**
     * Get clinics by user ID
     */
    public function getClinicsByUser(int $userId): Collection
    {
        return Clinic::with(['doctors', 'appointments'])
            ->where('user_id', $userId)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get open clinics at current time
     */
    public function getOpenClinics(): Collection
    {
        $currentTime = Carbon::now();
        
        return Clinic::with(['business', 'doctors'])
            ->where('is_open', true)
            ->where(function ($query) use ($currentTime) {
                $query->whereNull('opening_time')
                    ->orWhere('opening_time', '<=', $currentTime);
            })
            ->where(function ($query) use ($currentTime) {
                $query->whereNull('closing_time')
                    ->orWhere('closing_time', '>=', $currentTime);
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinic statistics
     */
    public function getClinicStats(int $clinicId): array
    {
        $clinic = $this->getClinicById($clinicId);
        
        if (!$clinic) {
            return [];
        }

        return [
            'total_doctors' => $clinic->doctors()->count(),
            'available_doctors' => $clinic->doctors()->where('is_available', true)->count(),
            'total_appointments' => $clinic->appointments()->count(),
            'pending_appointments' => $clinic->appointments()->where('status', 'pending')->count(),
            'confirmed_appointments' => $clinic->appointments()->where('status', 'confirmed')->count(),
            'completed_appointments' => $clinic->appointments()->where('status', 'completed')->count(),
            'cancelled_appointments' => $clinic->appointments()->where('status', 'cancelled')->count(),
            'today_appointments' => $clinic->appointments()
                ->whereDate('appointment_date', Carbon::today())
                ->count(),
            'this_week_appointments' => $clinic->appointments()
                ->whereBetween('appointment_date', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ])
                ->count(),
            'this_month_appointments' => $clinic->appointments()
                ->whereMonth('appointment_date', Carbon::now()->month)
                ->whereYear('appointment_date', Carbon::now()->year)
                ->count(),
        ];
    }

    /**
     * Get clinic's available doctors
     */
    public function getAvailableDoctors(int $clinicId): Collection
    {
        return Doctor::with(['user'])
            ->where('clinic_id', $clinicId)
            ->where('is_available', true)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get clinic's appointments for a specific date
     */
    public function getClinicAppointmentsByDate(int $clinicId, string $date): Collection
    {
        return Appointment::with(['customer', 'doctor'])
            ->where('clinic_id', $clinicId)
            ->whereDate('appointment_date', $date)
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get clinic's upcoming appointments
     */
    public function getUpcomingAppointments(int $clinicId, int $days = 7): Collection
    {
        return Appointment::with(['customer', 'doctor'])
            ->where('clinic_id', $clinicId)
            ->where('appointment_date', '>=', Carbon::now())
            ->where('appointment_date', '<=', Carbon::now()->addDays($days))
            ->whereIn('status', ['pending', 'confirmed'])
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Check if clinic is currently open
     */
    public function isClinicOpen(int $clinicId): bool
    {
        $clinic = $this->getClinicById($clinicId);
        
        if (!$clinic || !$clinic->is_open) {
            return false;
        }

        $currentTime = Carbon::now();
        
        // If no opening/closing times set, assume always open
        if (!$clinic->opening_time && !$clinic->closing_time) {
            return true;
        }

        // Check if current time is within opening hours
        if ($clinic->opening_time && $currentTime->lt($clinic->opening_time)) {
            return false;
        }

        if ($clinic->closing_time && $currentTime->gt($clinic->closing_time)) {
            return false;
        }

        return true;
    }

    /**
     * Get clinic's working hours information
     */
    public function getClinicWorkingHours(int $clinicId): array
    {
        $clinic = $this->getClinicById($clinicId);
        
        if (!$clinic) {
            return [];
        }

        return [
            'is_open' => $clinic->is_open,
            'opening_time' => $clinic->opening_time?->format('H:i'),
            'closing_time' => $clinic->closing_time?->format('H:i'),
            'closing_reason' => $clinic->closing_reason,
            'currently_open' => $this->isClinicOpen($clinicId),
        ];
    }

    /**
     * Search clinics by name or location
     */
    public function searchClinics(string $searchTerm): Collection
    {
        return Clinic::with(['user', 'doctors'])
            ->where('is_open', true)
            ->where(function ($query) use ($searchTerm) {
                $query->where('name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('floor_number', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('room_number', 'LIKE', "%{$searchTerm}%");
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinic contact information
     */
    public function getClinicContactInfo(int $clinicId): array
    {
        $clinic = $this->getClinicById($clinicId);
        
        if (!$clinic) {
            return [];
        }

        return [
            'name' => $clinic->name,
            'phone_number' => $clinic->phone_number,
            'location' => [
                'floor_number' => $clinic->floor_number,
                'room_number' => $clinic->room_number,
            ],
            'owner' => [
                'name' => $clinic->user->name,
                'email' => $clinic->user->email,
            ],
        ];
    }

    /**
     * Get clinics with most appointments this month
     */
    public function getBusiestClinics(int $limit = 10): Collection
    {
        return Clinic::with(['user'])
            ->withCount(['appointments' => function ($query) {
                $query->whereMonth('appointment_date', Carbon::now()->month)
                    ->whereYear('appointment_date', Carbon::now()->year);
            }])
            ->orderBy('appointments_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get clinic performance metrics
     */
    public function getClinicPerformanceMetrics(int $clinicId): array
    {
        $clinic = $this->getClinicById($clinicId);
        
        if (!$clinic) {
            return [];
        }

        $totalAppointments = $clinic->appointments()->count();
        $completedAppointments = $clinic->appointments()->where('status', 'completed')->count();
        $cancelledAppointments = $clinic->appointments()->where('status', 'cancelled')->count();

        return [
            'completion_rate' => $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100, 2) : 0,
            'cancellation_rate' => $totalAppointments > 0 ? round(($cancelledAppointments / $totalAppointments) * 100, 2) : 0,
            'total_appointments' => $totalAppointments,
            'completed_appointments' => $completedAppointments,
            'cancelled_appointments' => $cancelledAppointments,
            'average_appointments_per_day' => $this->getAverageAppointmentsPerDay($clinicId),
        ];
    }

    /**
     * Get average appointments per day for a clinic
     */
    private function getAverageAppointmentsPerDay(int $clinicId): float
    {
        $firstAppointment = Appointment::where('clinic_id', $clinicId)
            ->orderBy('appointment_date')
            ->first();

        if (!$firstAppointment) {
            return 0;
        }

        $daysSinceFirst = Carbon::parse($firstAppointment->appointment_date)->diffInDays(Carbon::now()) + 1;
        $totalAppointments = Appointment::where('clinic_id', $clinicId)->count();

        return round($totalAppointments / $daysSinceFirst, 2);
    }

    // ==================== CLINIC OFFERS METHODS ====================

    /**
     * Get all offers for a clinic
     */
    public function getClinicOffers(int $clinicId): Collection
    {
        return Offer::where('clinic_id', $clinicId)
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Get active offers for a clinic
     */
    public function getActiveClinicOffers(int $clinicId): Collection
    {
        $currentDate = Carbon::now();

        return Offer::where('clinic_id', $clinicId)
            ->where('start_date', '<=', $currentDate)
            ->where('end_date', '>=', $currentDate)
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Get upcoming offers for a clinic
     */
    public function getUpcomingClinicOffers(int $clinicId): Collection
    {
        return Offer::where('clinic_id', $clinicId)
            ->where('start_date', '>', Carbon::now())
            ->orderBy('start_date')
            ->get();
    }

    /**
     * Get expired offers for a clinic
     */
    public function getExpiredClinicOffers(int $clinicId): Collection
    {
        return Offer::where('clinic_id', $clinicId)
            ->where('end_date', '<', Carbon::now())
            ->orderBy('end_date', 'desc')
            ->get();
    }

    /**
     * Get offers by date range for a clinic
     */
    public function getClinicOffersByDateRange(int $clinicId, string $startDate, string $endDate): Collection
    {
        return Offer::where('clinic_id', $clinicId)
            ->whereBetween('start_date', [$startDate, $endDate])
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Search clinic offers by title or description
     */
    public function searchClinicOffers(int $clinicId, string $searchTerm): Collection
    {
        return Offer::where('clinic_id', $clinicId)
            ->where(function ($query) use ($searchTerm) {
                $query->where('title', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            })
            ->orderBy('start_date', 'desc')
            ->get();
    }

    /**
     * Get all active offers across all clinics
     */
    public function getAllActiveOffers(): Collection
    {
        $currentDate = Carbon::now();

        return Offer::with(['clinic'])
            ->where('start_date', '<=', $currentDate)
            ->where('end_date', '>=', $currentDate)
            ->orderBy('start_date', 'desc')
            ->get();
    }

    // ==================== CLINIC SERVICES METHODS ====================

    /**
     * Get all services for a clinic
     */
    public function getClinicServices(int $clinicId): Collection
    {
        return ClinicServiceModel::where('clinic_id', $clinicId)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinic service by ID
     */
    public function getClinicServiceById(int $serviceId): ?ClinicServiceModel
    {
        return ClinicServiceModel::with(['clinic'])
            ->find($serviceId);
    }

    /**
     * Search clinic services by name or description
     */
    public function searchClinicServices(int $clinicId, string $searchTerm): Collection
    {
        return ClinicServiceModel::where('clinic_id', $clinicId)
            ->where(function ($query) use ($searchTerm) {
                $query->where('name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get all services across all clinics
     */
    public function getAllClinicServices(): Collection
    {
        return ClinicServiceModel::with(['clinic'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Search services across all clinics
     */
    public function searchAllClinicServices(string $searchTerm): Collection
    {
        return ClinicServiceModel::with(['clinic'])
            ->where(function ($query) use ($searchTerm) {
                $query->where('name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('description', 'LIKE', "%{$searchTerm}%");
            })
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinics that offer a specific service
     */
    public function getClinicsOfferingService(string $serviceName): Collection
    {
        return Clinic::with(['user', 'services'])
            ->whereHas('services', function ($query) use ($serviceName) {
                $query->where('name', 'LIKE', "%{$serviceName}%");
            })
            ->where('is_open', true)
            ->orderBy('name')
            ->get();
    }

    /**
     * Get clinic's complete profile with offers and services
     */
    public function getClinicCompleteProfile(int $clinicId): array
    {
        $clinic = Clinic::with(['user', 'doctors', 'offers', 'services'])
            ->find($clinicId);

        if (!$clinic) {
            return [];
        }

        return [
            'basic_info' => [
                'id' => $clinic->id,
                'name' => $clinic->name,
                'phone_number' => $clinic->phone_number,
                'location' => [
                    'floor_number' => $clinic->floor_number,
                    'room_number' => $clinic->room_number,
                ],
                'is_open' => $clinic->is_open,
                'closing_reason' => $clinic->closing_reason,
            ],
            'working_hours' => [
                'opening_time' => $clinic->opening_time?->format('H:i'),
                'closing_time' => $clinic->closing_time?->format('H:i'),
                'currently_open' => $this->isClinicOpen($clinicId),
            ],
            'owner_info' => [
                'name' => $clinic->user->name,
                'email' => $clinic->user->email,
            ],
            'doctors' => $clinic->doctors->map(function ($doctor) {
                return [
                    'id' => $doctor->id,
                    'name' => trim($doctor->first_name . ' ' . $doctor->last_name),
                    'specialization' => $doctor->specialization,
                    'is_available' => $doctor->is_available,
                ];
            }),
            'active_offers' => $clinic->offers->filter(function ($offer) {
                $currentDate = Carbon::now();
                return $offer->start_date <= $currentDate && $offer->end_date >= $currentDate;
            })->values(),
            'services' => $clinic->services->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'description' => $service->description,
                    'price' => $service->price,
                    'service_url' => $service->service_url,
                ];
            }),
            'statistics' => $this->getClinicStats($clinicId),
        ];
    }

    /**
     * البحث عن عيادة بالاسم
     */
    public function searchClinicsByName(string $clinicName)
    {
        return Clinic::where('name', 'LIKE', "%{$clinicName}%")
            ->with(['doctors', 'services'])
            ->get();
    }

    /**
     * البحث عن عيادة بالاسم للمستخدم المحدد
     */
    public function searchClinicsByNameForUser(string $clinicName, int $userId)
    {
        return Clinic::where('name', 'LIKE', "%{$clinicName}%")
            ->whereHas('business', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->with(['doctors', 'services', 'business'])
            ->get();
    }

    /**
     * الحصول على العيادات المفتوحة للمستخدم المحدد
     */
    public function getOpenClinicsForUser(int $userId)
    {
        return Clinic::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->with(['doctors', 'services', 'business'])
        ->get()
        ->filter(function($clinic) {
            return $this->isClinicOpen($clinic->id);
        });
    }


}
