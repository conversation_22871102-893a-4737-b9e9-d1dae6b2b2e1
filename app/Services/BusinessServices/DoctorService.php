<?php

namespace App\Services\BusinessServices;

use App\Models\Doctor;
use App\Models\Clinic;
use App\Models\Appointment;
use App\Models\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DoctorService
{
    /**
     * Get all available doctors
     */
    public function getAllAvailableDoctors(): Collection
    {
        return Doctor::with(['business', 'clinic'])
            ->where('is_available', true)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get doctor by ID with relationships
     */
    public function getDoctorById(int $doctorId): ?Doctor
    {
        return Doctor::with(['business', 'clinic', 'appointments'])
            ->find($doctorId);
    }

    /**
     * Get doctors by clinic ID
     */
    public function getDoctorsByClinic(int $clinicId): Collection
    {
        return Doctor::with(['business', 'appointments'])
            ->where('clinic_id', $clinicId)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get available doctors by clinic ID
     */
    public function getAvailableDoctorsByClinic(int $clinicId): Collection
    {
        return Doctor::with(['business'])
            ->where('clinic_id', $clinicId)
            ->where('is_available', true)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get doctors by specialization
     */
    public function getDoctorsBySpecialization(string $specialization): Collection
    {
        return Doctor::with(['user', 'clinic'])
            ->where('specialization', 'LIKE', "%{$specialization}%")
            ->where('is_available', true)
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Search doctors by name, specialization, or clinic
     */
    public function searchDoctors(string $searchTerm): Collection
    {
        return Doctor::with(['user', 'clinic'])
            ->where('is_available', true)
            ->where(function ($query) use ($searchTerm) {
                $query->where('first_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('last_name', 'LIKE', "%{$searchTerm}%")
                    ->orWhere('specialization', 'LIKE', "%{$searchTerm}%")
                    ->orWhereHas('clinic', function ($clinicQuery) use ($searchTerm) {
                        $clinicQuery->where('name', 'LIKE', "%{$searchTerm}%");
                    });
            })
            ->orderBy('first_name')
            ->get();
    }

    /**
     * Get doctor's full information
     */
    public function getDoctorFullInfo(int $doctorId): array
    {
        $doctor = $this->getDoctorById($doctorId);
        
        if (!$doctor) {
            return [];
        }

        return [
            'personal_info' => [
                'id' => $doctor->id,
                'first_name' => $doctor->first_name,
                'last_name' => $doctor->last_name,
                'full_name' => trim($doctor->first_name . ' ' . $doctor->last_name),
                'specialization' => $doctor->specialization,
                'experience' => $doctor->experience,
                'education' => $doctor->education,
                'certifications' => $doctor->certifications,
                'languages' => $doctor->languages,
            ],
            'availability' => [
                'is_available' => $doctor->is_available,
                'unavailability_reason' => $doctor->unavailability_reason,
                'working_days' => $doctor->working_days,
                'available_from' => $doctor->available_from?->format('H:i'),
                'available_to' => $doctor->available_to?->format('H:i'),
            ],
            'clinic_info' => [
                'clinic_name' => $doctor->clinic->name,
                'clinic_id' => $doctor->clinic->id,
                'clinic_phone' => $doctor->clinic->phone_number,
                'clinic_location' => [
                    'floor' => $doctor->clinic->floor_number,
                    'room' => $doctor->clinic->room_number,
                ],
            ],
            'business_info' => [
                'business_name' => $doctor->business->name,
                'business_email' => $doctor->business->email,
                'business_phone' => $doctor->business->phone,
            ],
        ];
    }

    /**
     * Get doctor's statistics
     */
    public function getDoctorStats(int $doctorId): array
    {
        $doctor = $this->getDoctorById($doctorId);
        
        if (!$doctor) {
            return [];
        }

        return [
            'total_appointments' => $doctor->appointments()->count(),
            'pending_appointments' => $doctor->appointments()->where('status', 'pending')->count(),
            'confirmed_appointments' => $doctor->appointments()->where('status', 'confirmed')->count(),
            'completed_appointments' => $doctor->appointments()->where('status', 'completed')->count(),
            'cancelled_appointments' => $doctor->appointments()->where('status', 'cancelled')->count(),
            'today_appointments' => $doctor->appointments()
                ->whereDate('appointment_date', Carbon::today())
                ->count(),
            'this_week_appointments' => $doctor->appointments()
                ->whereBetween('appointment_date', [
                    Carbon::now()->startOfWeek(),
                    Carbon::now()->endOfWeek()
                ])
                ->count(),
            'this_month_appointments' => $doctor->appointments()
                ->whereMonth('appointment_date', Carbon::now()->month)
                ->whereYear('appointment_date', Carbon::now()->year)
                ->count(),
        ];
    }

    /**
     * Get doctor's upcoming appointments
     */
    public function getDoctorUpcomingAppointments(int $doctorId, int $days = 7): Collection
    {
        return Appointment::with(['customer', 'clinic'])
            ->where('doctor_id', $doctorId)
            ->where('appointment_date', '>=', Carbon::now())
            ->where('appointment_date', '<=', Carbon::now()->addDays($days))
            ->whereIn('status', ['pending', 'confirmed'])
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Get doctor's appointments for a specific date
     */
    public function getDoctorAppointmentsByDate(int $doctorId, string $date): Collection
    {
        return Appointment::with(['customer', 'clinic'])
            ->where('doctor_id', $doctorId)
            ->whereDate('appointment_date', $date)
            ->orderBy('appointment_date')
            ->get();
    }

    /**
     * Check if doctor is available at specific time
     */
    public function isDoctorAvailableAt(int $doctorId, Carbon $dateTime): bool
    {
        $doctor = $this->getDoctorById($doctorId);
        
        if (!$doctor || !$doctor->is_available) {
            return false;
        }

        // Check if it's within working days
        if ($doctor->working_days && !in_array($dateTime->dayOfWeek, $doctor->working_days)) {
            return false;
        }

        // Check if it's within working hours
        if ($doctor->available_from && $dateTime->format('H:i') < $doctor->available_from->format('H:i')) {
            return false;
        }

        if ($doctor->available_to && $dateTime->format('H:i') > $doctor->available_to->format('H:i')) {
            return false;
        }

        // Check if doctor has conflicting appointments
        $conflictingAppointments = Appointment::where('doctor_id', $doctorId)
            ->where('appointment_date', $dateTime)
            ->whereIn('status', ['pending', 'confirmed'])
            ->count();

        return $conflictingAppointments === 0;
    }

    /**
     * Get doctor's available time slots for a specific date
     */
    public function getDoctorAvailableSlots(int $doctorId, string $date, int $slotDurationMinutes = 30): array
    {
        $doctor = $this->getDoctorById($doctorId);
        
        if (!$doctor || !$doctor->is_available) {
            return [];
        }

        $requestedDate = Carbon::parse($date);
        
        // Check if it's a working day
        if ($doctor->working_days && !in_array($requestedDate->dayOfWeek, $doctor->working_days)) {
            return [];
        }

        $startTime = $doctor->available_from ? 
            Carbon::parse($date . ' ' . $doctor->available_from->format('H:i:s')) : 
            Carbon::parse($date . ' 09:00:00');
            
        $endTime = $doctor->available_to ? 
            Carbon::parse($date . ' ' . $doctor->available_to->format('H:i:s')) : 
            Carbon::parse($date . ' 17:00:00');

        // Get existing appointments for the date
        $existingAppointments = $this->getDoctorAppointmentsByDate($doctorId, $date)
            ->whereIn('status', ['pending', 'confirmed'])
            ->pluck('appointment_date')
            ->map(function ($date) {
                return Carbon::parse($date);
            });

        $availableSlots = [];
        $currentSlot = $startTime->copy();

        while ($currentSlot->lt($endTime)) {
            $slotEnd = $currentSlot->copy()->addMinutes($slotDurationMinutes);
            
            // Check if this slot conflicts with any existing appointment
            $hasConflict = $existingAppointments->contains(function ($appointmentTime) use ($currentSlot, $slotEnd) {
                return $appointmentTime->between($currentSlot, $slotEnd, false);
            });

            if (!$hasConflict) {
                $availableSlots[] = [
                    'start_time' => $currentSlot->format('H:i'),
                    'end_time' => $slotEnd->format('H:i'),
                    'datetime' => $currentSlot->toDateTimeString(),
                ];
            }

            $currentSlot->addMinutes($slotDurationMinutes);
        }

        return $availableSlots;
    }

    /**
     * Get doctors with most appointments this month
     */
    public function getBusiestDoctors(int $limit = 10): Collection
    {
        return Doctor::with(['user', 'clinic'])
            ->withCount(['appointments' => function ($query) {
                $query->whereMonth('appointment_date', Carbon::now()->month)
                    ->whereYear('appointment_date', Carbon::now()->year);
            }])
            ->orderBy('appointments_count', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get all unique specializations
     */
    public function getAllSpecializations(): array
    {
        return Doctor::distinct()
            ->pluck('specialization')
            ->filter()
            ->sort()
            ->values()
            ->toArray();
    }

    /**
     * Get doctor performance metrics
     */
    public function getDoctorPerformanceMetrics(int $doctorId): array
    {
        $doctor = $this->getDoctorById($doctorId);
        
        if (!$doctor) {
            return [];
        }

        $totalAppointments = $doctor->appointments()->count();
        $completedAppointments = $doctor->appointments()->where('status', 'completed')->count();
        $cancelledAppointments = $doctor->appointments()->where('status', 'cancelled')->count();

        return [
            'completion_rate' => $totalAppointments > 0 ? round(($completedAppointments / $totalAppointments) * 100, 2) : 0,
            'cancellation_rate' => $totalAppointments > 0 ? round(($cancelledAppointments / $totalAppointments) * 100, 2) : 0,
            'total_appointments' => $totalAppointments,
            'completed_appointments' => $completedAppointments,
            'cancelled_appointments' => $cancelledAppointments,
            'average_appointments_per_day' => $this->getAverageAppointmentsPerDay($doctorId),
        ];
    }

    /**
     * Get average appointments per day for a doctor
     */
    private function getAverageAppointmentsPerDay(int $doctorId): float
    {
        $firstAppointment = Appointment::where('doctor_id', $doctorId)
            ->orderBy('appointment_date')
            ->first();

        if (!$firstAppointment) {
            return 0;
        }

        $daysSinceFirst = Carbon::parse($firstAppointment->appointment_date)->diffInDays(Carbon::now()) + 1;
        $totalAppointments = Appointment::where('doctor_id', $doctorId)->count();

        return round($totalAppointments / $daysSinceFirst, 2);
    }

    /**
     * البحث عن طبيب بالاسم
     */
    public function searchDoctorsByName(string $doctorName)
    {
        return Doctor::where(function($query) use ($doctorName) {
            $query->where('first_name', 'LIKE', "%{$doctorName}%")
                  ->orWhere('last_name', 'LIKE', "%{$doctorName}%")
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$doctorName}%"]);
        })
        ->with(['clinic'])
        ->get();
    }

    /**
     * البحث عن طبيب بالاسم للمستخدم المحدد
     */
    public function searchDoctorsByNameForUser(string $doctorName, int $userId)
    {
        return Doctor::where(function($query) use ($doctorName) {
            $query->where('first_name', 'LIKE', "%{$doctorName}%")
                  ->orWhere('last_name', 'LIKE', "%{$doctorName}%")
                  ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$doctorName}%"]);
        })
        ->whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->with(['clinic', 'business'])
        ->get();
    }

    /**
     * الحصول على أطباء تخصص معين للمستخدم المحدد
     */
    public function getDoctorsBySpecializationForUser(string $specialization, int $userId)
    {
        return Doctor::where('specialization', 'LIKE', "%{$specialization}%")
            ->whereHas('business', function($query) use ($userId) {
                $query->where('user_id', $userId);
            })
            ->with(['clinic', 'business'])
            ->where('is_available', true)
            ->get();
    }

    /**
     * الحصول على الأطباء المتاحين للمستخدم المحدد
     */
    public function getAvailableDoctorsForUser(int $userId, int $limit = 10)
    {
        return Doctor::whereHas('business', function($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->with(['clinic', 'business'])
        ->where('is_available', true)
        ->take($limit)
        ->get();
    }
}
